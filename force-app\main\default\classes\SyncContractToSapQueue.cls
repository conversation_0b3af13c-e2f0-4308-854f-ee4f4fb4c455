public class SyncContractToSapQueue implements Queueable,Database.AllowsCallouts  {
    private Set<Id> contractids;
    public SyncContractToSapQueue(Set<Id> contractids) {
        this.contractids = contractids;
    }

    public void execute(QueueableContext context) {
        try{
            //验证合同接口必填字段
            List<Contract> contractList = [ SELECT id,SapContractNo__c,ContractAutoNo__c,(SELECT id from CollectionPlan__r) from Contract where id IN:contractIds ];
            if(contractList != null && contractList.size() > 0){
                Contract con = contractList[0];
                if(con != null && con.SapContractNo__c != null && con.ContractAutoNo__c != null && con.CollectionPlan__r != null && con.CollectionPlan__r.size()>0){
                    Interface_CRMSyncContractToSAP contractSync = new Interface_CRMSyncContractToSAP(con.id);
                }else{
                    Interface_Log__c queueLog = new Interface_Log__c();
                    queueLog.InterfaceName__c = 'SyncContractToSapQueue';
                    queueLog.Status__c = 'Failed';
                    queueLog.InterfaceType__c = 'queue';
                    queueLog.RecordIDs__c = String.join(contractids, ';');
                    queueLog.CallTime__c = System.now();
                    queueLog.ErrorMessage__c = '已有sap合同号 或者 合同编码、收款计划数据缺失，无法同步合同，请跟进处理。合同ID：'+ con.id ;
                    insert queueLog;
                    //CWUtility.sendEmailToAdmin(body);
                }
            }
        }catch(Exception ex){
            Interface_Log__c queueLog = new Interface_Log__c();
            queueLog.InterfaceName__c = 'SyncContractToSapQueue';
            queueLog.Status__c = 'Failed';
            queueLog.InterfaceType__c = 'queue';
            queueLog.RecordIDs__c = String.join(contractids, ';');
            queueLog.CallTime__c = System.now();
            queueLog.ErrorMessage__c = ex.getMessage();
            insert queueLog;
            //CWUtility.sendEmailToAdmin(errorbody);
        }

    }
}