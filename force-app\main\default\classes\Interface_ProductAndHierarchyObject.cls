public class Interface_ProductAndHierarchyObject {
    public String uuid;      // CHAR 32
    public String znumb;    // CHAR 30
    public String sysid;     // CHAR 20
    public List<ProductItem> item; // 嵌套的产品列表

    // 定义物料项子类
    public class ProductItem {
        // 主表字段
        public String MTART;  // 物料类型
        public String MATNR;  // 物料编码
        public String MAKTX;  // 物料描述-中文
        public String MAKTX_EN; // 物料描述-英文
        public String MEINS;  // 基本计量单位
        public String MATKL;  // 物料组
        public String SPART;  // 产品组
        public String PRDHA;  // 产品层次，exp:0102
        public String STUFE;  // 产品层号, 2
        
        // 产品层次结构(最多5级)
        public String ZPRODH1; // 产品层次1
        public String ZSTUFE1; // 产品层号1
        public String VTEXT1;  // 产品描述1
        public String ZPRODH2; // 产品层次2
        public String ZSTUFE2; // 产品层号2
        public String VTEXT2;  // 产品描述2
        public String ZPRODH3; // 产品层次3
        public String ZSTUFE3; // 产品层号3
        public String VTEXT3;  // 产品描述3
        public String ZPRODH4; // 产品层次4
        public String ZSTUFE4; // 产品层号4
        public String VTEXT4;  // 产品描述4
        public String ZPRODH5; // 产品层次5
        public String ZSTUFE5; // 产品层号5
        public String VTEXT5;  // 产品描述5


    }
}