public with sharing class QuotePrintPDFController {
    // 用于存储报价ID
    private Id quoteId;
    
    // 用于存储报价对象
    public Quote quote { get; private set; }
    
    // 用于存储账户的银行信息
    public BankInfo__c bankInfo { get; private set; }

    
    // 用于存储报价行项目及其报价方式的包装类列表
    public List<QuoteLineItemWrapper> quoteLineItemToUnitAmountWrappers { get; private set; }
    public List<QuoteLineItemWrapper> quoteLineItemToProductDiscountWrappers { get; private set; }
    public List<QuoteLineItemWrapper> quoteLineItemToUnitAmountSharedUsageLadderWrappers { get; private set; }
    public List<QuoteLineItemWrapper> quoteLineItemToUsageLadderWrappers { get; private set; }
    public List<QuoteLineItemWrapper> quoteLineItemToMinAmountSharedAmountDiscountWrappers { get; private set; }

    
    // 用于存储产品组数据
    public List<ProductGroupWrapper> productGroups { get; private set; }
    
    // 用于存储合同期限
    public String contractDuration { get; private set; }
    // 付款方式
    public String paymentMethod { get; private set; }
    // 付款周期
    public String paymentCyle { get; private set; }
    
    // 用于确定是否存在金额封顶报价方式
    public Boolean hasAmountCapMethod { get; private set; }
    
    // 用于确定是否存在固定用量*固定单价报价方式
    public Boolean hasFixedDosageMethod { get; private set; }
    
    // 用于确定是否存在保底单价*保底数量报价方式
    public Boolean hasMinimumPriceMethod { get; private set; }
    
    // 用于确定是否存在产品折扣报价方式
    public Boolean hasDiscountMethod { get; private set; }
    
    // 构造函数
    public QuotePrintPDFController(ApexPages.StandardController controller) {
        // 获取报价ID
        this.quoteId = controller.getId();
        
        // 初始化包装器列表
        this.quoteLineItemToUnitAmountWrappers = new List<QuoteLineItemWrapper>();
        this.quoteLineItemToProductDiscountWrappers = new List<QuoteLineItemWrapper>();
        this.quoteLineItemToUnitAmountSharedUsageLadderWrappers = new List<QuoteLineItemWrapper>();
        this.quoteLineItemToUsageLadderWrappers = new List<QuoteLineItemWrapper>();
        this.quoteLineItemToMinAmountSharedAmountDiscountWrappers = new List<QuoteLineItemWrapper>();

        this.productGroups = new List<ProductGroupWrapper>();
        
        // 初始化报价方式标志
        this.hasAmountCapMethod = false;
        this.hasFixedDosageMethod = false;
        this.hasMinimumPriceMethod = false;
        this.hasDiscountMethod = false;
        this.paymentMethod='';
        this.paymentCyle='';
        
        // 如果报价ID不为空，则加载数据
        if (this.quoteId != null) {
            loadQuoteData();
        
            loadQuoteLineItems();
            loadProductGroups();
            calculateContractDuration();
        }
    }
    
    // 加载报价数据
    private void loadQuoteData() {
        try {
            // 查询报价详细信息
            this.quote = [
                SELECT Id, Name, QuoteNumber, Account.Name, StartDate__c, EndDate__c, TotalPrice,
                Account.Customer_Mailing_Address__c, Account.Legal_Representative__c,Account.CompanyAddress__c,
                Account.Business_License_Number__c, Account.Customer_ICP_Filing_Number__c,
                Account.Customer_Subsidiary__c,Account.PostalCode__c,prepayment__c,Contract_Cur__c,
                Expense_Settlement_Method__c, toLabel(Expense_Settlement_Method__c) paymentMethodLabel,DepositAmount__c, Exchange_Rate__c,
                Payment_Way__c, MSP_Company_Name__c,Payment_Cycle__c,toLabel(Payment_Cycle__c) paymentCycleLabel,
                Invoice_Customer_Title__c,Cucstomer_Contract__r.name,Cucstomer_Contract__r.phone,Cucstomer_Contract__r.Email,
                Customer_Tech_Contact__r.name,Customer_Tech_Contact__r.phone,Customer_Tech_Contact__r.Email,
                Customer_Finance_Contact__r.name,Customer_Finance_Contact__r.phone,Customer_Finance_Contact__r.Email,
                MSP_Technical_Contact__r.name,MSP_Technical_Contact__r.phone,MSP_Technical_Contact__r.Email,
                MSP_Contract_Contact__r.name,MSP_Contract_Contact__r.phone,MSP_Contract_Contact__r.Email,
                MSP_Finance_Contact__r.name,MSP_Finance_Contact__r.phone,MSP_Finance_Contact__r.Email,
                 PartyB_Signing_Company__r.Registered_Country_Province__c,PartyB_Signing_Company__r.Street__c, 
                 PartyB_Signing_Company__r.CompanyAddress__c,
                PartyB_Signing_Company__r.PostalCode__c,PartyB_Signing_Company__r.Name,Account.Customer_Domain__c,
                Owner.Name,Owner.phone, owner.Email
         FROM Quote
                WHERE Id = :this.quoteId
                LIMIT 1
            ];
            this.paymentCyle=String.valueOf(this.quote.get('paymentCycleLabel'));
            this.paymentMethod=String.valueOf(this.quote.get('paymentMethodLabel'));

            if (quote != null && quote.AccountId != null) {
                // 查询账户关联的银行信息
                /*List<BankInfo__c> bankInfos = [
                    SELECT Id, Name,BankCode__c, Account__c, 
                    Bank_Swift_Code__c, BankContury__c, Bank_Identification__c,
                    Bank_Account_No__c
                    FROM BankInfo__c
                    WHERE Account__c = :quote.AccountId
                    LIMIT 1
                ];
                
                if (!bankInfos.isEmpty()) {
                    this.bankInfo = bankInfos[0];
                }*/
            }
        } catch (Exception e) {
            ApexPages.addMessage(new ApexPages.Message(
                ApexPages.Severity.ERROR, 
                '加载报价数据出错: ' + e.getMessage()
            ));
        }
    }
    
    
    // 计算合同期限
    private void calculateContractDuration() {
        if (quote != null && quote.StartDate__c != null && quote.EndDate__c != null) {
            // 计算日期差距（月数）
            Integer startMonth = quote.StartDate__c.month();
            Integer startYear = quote.StartDate__c.year();
            Integer endMonth = quote.EndDate__c.month();
            Integer endYear = quote.EndDate__c.year();
            
            Integer totalMonths = (endYear - startYear) * 12 + (endMonth - startMonth);
            
            // 计算具体的天数差距，以处理不足整月的情况
            Integer startDay = quote.StartDate__c.day();
            Integer endDay = quote.EndDate__c.day();
            
            // 如果结束日期的天数小于开始日期的天数，减去1个月（不足整月）
            if (endDay < startDay) {
                totalMonths--;
            }
            
            // 根据月数确定合同期限
            if (totalMonths <= 0) {
                contractDuration = '不足1个月';
            } else if (totalMonths < 12) {
                if (totalMonths == 6) {
                    contractDuration = '半年';
                } else {
                    contractDuration = totalMonths + ' 个月';
                }
            } else {
                Integer years = totalMonths / 12;
                Integer remainingMonths = Math.mod(totalMonths, 12);
                
                if (remainingMonths == 0) {
                    contractDuration = years + ' 年';
                } else if (remainingMonths == 6) {
                    contractDuration = years + ' 年半';
                } else {
                    contractDuration = years + ' 年 ' + remainingMonths + ' 个月';
                }
            }
        } else {
            contractDuration = '未定义';
        }
    }
    
    // 加载报价行项目及其报价方式
    private void loadQuoteLineItems() {
        try {
            // 查询报价行项目，只查询非产品组的产品 (ISGROUP=false)
            List<QuoteLineItem> quoteLineItems = [
                SELECT Id, Product2Id, Product2.Name, Product2.Description,
                       UnitPrice, Tax_Rate__c, Account_ID__c, Profit_Statement__c,toLabel(Region__c),
                       QuoteLineStartDate__c,QuoteLineEndDate__c,
                       QuoteId
                FROM QuoteLineItem
                WHERE QuoteId = :this.quoteId AND ISGROUP__c = false
                ORDER BY CreatedDate ASC
            ];


            Map<Id,QuoteLineItem> quoteLineItemMap= new Map<Id,QuoteLineItem>();
            Set<Id> productIds = new Set<Id>();
            for (QuoteLineItem qli : quoteLineItems) {
                quoteLineItemMap.put(qli.Id,qli);
                if (qli.Product2Id != null) {
                    productIds.add(qli.Product2Id);
                }
            }

            // 查询产品牌价对象
            Map<Id, ProductPrice__c> productToPriceMap = new Map<Id, ProductPrice__c>();
            for (ProductPrice__c price : [
                SELECT Id, Product__c, Amount__c, CurrencyIsoCode
                FROM ProductPrice__c
                WHERE Product__c IN :productIds
            ]) {
                productToPriceMap.put(price.Product__c, price);
            }
            
            // 查询报价方式
            List<Quotation_Method__c> qouteMethods = [
                SELECT Id, Quote_Line_Item_ID__c, Method__c, 
                    GuaranteedMin_Amount__c, Minimum_ProdUnitPrice__c,
                    Minimum_Amout__c, FixAmount__c, Amount_Cap__c,
                    Discount_Factor__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c
                FROM Quotation_Method__c
                WHERE Quote_Line_Item_ID__c IN :quoteLineItemMap.keySet() AND ISGROUP__c = false
                ORDER BY CreatedDate
            ];
            
            // 按报价项ID分组

            for (Quotation_Method__c qm : qouteMethods) {
                QuoteLineItemWrapper wrapper = new QuoteLineItemWrapper();
                wrapper.quoteLineItem = quoteLineItemMap.get(qm.Quote_Line_Item_ID__c);
                wrapper.quoteMethod = qm;

                // 添加产品牌价信息
                if (wrapper.quoteLineItem != null && wrapper.quoteLineItem.Product2Id != null &&
                    productToPriceMap.containsKey(wrapper.quoteLineItem.Product2Id)) {
                    ProductPrice__c priceInfo = productToPriceMap.get(wrapper.quoteLineItem.Product2Id);
                    wrapper.listPrice = priceInfo.Amount__c;
                    wrapper.listPriceCurrency = priceInfo.CurrencyIsoCode;
                }

                if (qm.Method__c == '1') {
                    this.quoteLineItemToUnitAmountWrappers.add(wrapper);
                } else if (qm.Method__c == '2') {
                    this.quoteLineItemToProductDiscountWrappers.add(wrapper);
                } else if (qm.Method__c == '3') {
                    this.quoteLineItemToUsageLadderWrappers.add(wrapper);
                } else if (qm.Method__c == '5') {
                    this.quoteLineItemToMinAmountSharedAmountDiscountWrappers.add(wrapper);
                } else if (qm.Method__c == '6') {
                    this.quoteLineItemToUnitAmountSharedUsageLadderWrappers.add(wrapper);
                }

            }
            
           
        } catch (Exception e) {
            ApexPages.addMessage(new ApexPages.Message(
                ApexPages.Severity.ERROR, 
                '加载报价行项目数据出错: ' + e.getMessage()
            ));
        }
    }
    
    // 加载产品组数据
    private void loadProductGroups() {
        try {
            // 获取报价单下的所有产品组产品(ISGROUP=true)
            List<QuoteLineItem> groupQuoteLineItems = [
                SELECT Id, Product2Id, Product2.Name, Product2.Description, UnitPrice,
                       Tax_Rate__c, Account_ID__c, Profit_Statement__c,
                       Product_Group__c, Product_Group__r.QuotationMethod_Ladder__c,
                       Product_Group__r.QuotationMethod_Ladder__r.Method__c, ISGROUP__c
                FROM QuoteLineItem
                WHERE QuoteId = :quoteId AND Product_Group__c != null AND ISGROUP__c = true
                ORDER BY CreatedDate ASC
            ];

            // 收集产品组中的所有产品ID，用于查询产品牌价
            Set<Id> groupProductIds = new Set<Id>();
            for (QuoteLineItem qli : groupQuoteLineItems) {
                if (qli.Product2Id != null) {
                    groupProductIds.add(qli.Product2Id);
                }
            }

            // 查询产品组产品的牌价对象
            Map<Id, ProductPrice__c> groupProductToPriceMap = new Map<Id, ProductPrice__c>();
            for (ProductPrice__c price : [
                SELECT Id, Product__c, Amount__c, CurrencyIsoCode
                FROM ProductPrice__c
                WHERE Product__c IN :groupProductIds
            ]) {
                groupProductToPriceMap.put(price.Product__c, price);
            }

            Map<Id, Quotation_Method__c> groupMethodMap = new Map<Id, Quotation_Method__c>();
            
            // 查询所有报价方式数据
            // 1. 保底金额+共享阶梯金额折扣落区组合报价方式
            Map<Id, Quotation_Method__c> groupMinimumAmountQuotations = new Map<Id, Quotation_Method__c>();
            List<Quotation_Method__c> allMinimumAmountQuotations = [
                SELECT Id, Product_Group__c, Method__c, GuaranteedMin_Amount__c, ISGROUP__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true)
                AND Method__c = '5' AND ISGROUP__c = true
            ];
            
            for (Quotation_Method__c qm : allMinimumAmountQuotations) {
                if (qm.Product_Group__c != null) {
                    groupMinimumAmountQuotations.put(qm.Product_Group__c, qm);
                    groupMethodMap.put(qm.Product_Group__c, qm);
                }
            }
            
            // 2. 保底单价*保底数量+共享阶梯用量单价落区组合报价方式
            Map<Id, Quotation_Method__c> groupMinimumUnitPriceQuotations = new Map<Id, Quotation_Method__c>();
            List<Quotation_Method__c> allMinimumUnitPriceQuotations = [
                SELECT Id, Product_Group__c, Method__c, Minimum_ProdUnitPrice__c, Minimum_Amout__c, ISGROUP__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true)
                AND Method__c = '6' AND ISGROUP__c = true
            ];
            
            for (Quotation_Method__c qm : allMinimumUnitPriceQuotations) {
                if (qm.Product_Group__c != null) {
                    groupMinimumUnitPriceQuotations.put(qm.Product_Group__c, qm);
                    groupMethodMap.put(qm.Product_Group__c, qm);

                }
            }
            
            // 3. 阶梯用量单价方式
            Map<Id, Quotation_Method__c> groupUsageLadderQuotations = new Map<Id, Quotation_Method__c>();
            List<Quotation_Method__c> allUsageLadderQuotations = [
                SELECT Id, Product_Group__c, Method__c, ISGROUP__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true)
                AND Method__c = '3' AND ISGROUP__c = true
            ];
            
            for (Quotation_Method__c qm : allUsageLadderQuotations) {
                if (qm.Product_Group__c != null) {
                    groupUsageLadderQuotations.put(qm.Product_Group__c, qm);
                    groupMethodMap.put(qm.Product_Group__c, qm);

                }
            }
            
            // 4. 产品折扣报价方式
            Map<Id, Quotation_Method__c> groupProductDiscountQuotations = new Map<Id, Quotation_Method__c>();
            List<Quotation_Method__c> allProductDiscountQuotations = [
                SELECT Id, Product_Group__c, Method__c, Discount_Factor__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c, ISGROUP__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true)
                AND Method__c = '2' AND ISGROUP__c = true
            ];
            
            for (Quotation_Method__c qm : allProductDiscountQuotations) {
                if (qm.Product_Group__c != null) {
                    groupProductDiscountQuotations.put(qm.Product_Group__c, qm);
                    groupMethodMap.put(qm.Product_Group__c, qm);

                }
            }

            // 5. 单价*数量方式
            Map<Id, Quotation_Method__c> groupUnitAmountQuotations = new Map<Id, Quotation_Method__c>();
            List<Quotation_Method__c> allUnitAmountQuotations = [
                SELECT Id, Product_Group__c, Method__c, Discount_Factor__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c, ISGROUP__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN (SELECT Product_Group__c FROM QuoteLineItem WHERE QuoteId = :quoteId AND ISGROUP__c = true)
                AND Method__c = '1' AND ISGROUP__c = true
            ];
            
            for (Quotation_Method__c qm : allUnitAmountQuotations) {
                if (qm.Product_Group__c != null) {
                    groupUnitAmountQuotations.put(qm.Product_Group__c, qm);
                    groupMethodMap.put(qm.Product_Group__c, qm);

                }
            }


            
            // 获取所有相关的阶梯报价行
            Map<Id, List<Ladder_Line__c>> ladderLinesByLadderDiscount = new Map<Id, List<Ladder_Line__c>>();
            
            if (!groupQuoteLineItems.isEmpty()) {
                Set<Id> ladderDiscountIds = new Set<Id>();
                for (QuoteLineItem qli : groupQuoteLineItems) {
                    if (qli.Product_Group__c != null) {
                        ladderDiscountIds.add(qli.Product_Group__c);
                    }
                }
                
                if (!ladderDiscountIds.isEmpty()) {
                    for (Ladder_Line__c line : [
                        SELECT Id, Product_Group__c, Up_Limit__c, Down_Limit__c, 
                               Unit__c, Discount__c, Calculation_Method__c
                        FROM Ladder_Line__c
                        WHERE Product_Group__c IN :ladderDiscountIds
                    ]) {
                        if (!ladderLinesByLadderDiscount.containsKey(line.Product_Group__c)) {
                            ladderLinesByLadderDiscount.put(line.Product_Group__c, new List<Ladder_Line__c>());
                        }
                        ladderLinesByLadderDiscount.get(line.Product_Group__c).add(line);
                    }
                }
            }
            
            // 将产品按报价方式分组
            Map<Id, List<QuoteLineItem>> itemsByQuotationMethod = new Map<Id, List<QuoteLineItem>>();
            
            for (QuoteLineItem qli : groupQuoteLineItems) {
                Id quotationMethodId = groupMethodMap.get(qli.Product_Group__c).Id;
                if (quotationMethodId != null) {
                    if (!itemsByQuotationMethod.containsKey(quotationMethodId)) {
                        itemsByQuotationMethod.put(quotationMethodId, new List<QuoteLineItem>());
                    }
                    itemsByQuotationMethod.get(quotationMethodId).add(qli);
                }
            }
            
            // 构建产品组包装器
            Integer groupNumber = 1;
            for (Id quotationMethodId : itemsByQuotationMethod.keySet()) {
                ProductGroupWrapper groupWrapper = new ProductGroupWrapper();
                groupWrapper.groupNumber = groupNumber++;
                
                // 处理产品
                groupWrapper.products = new List<QuoteLineItem>();
                Id productGroupId = null; // 记录当前产品组的Product_Group__c ID

                for (QuoteLineItem qli : itemsByQuotationMethod.get(quotationMethodId)) {
                    groupWrapper.products.add(qli);

                    // 添加产品牌价信息到产品组包装器
                    if (qli.Product2Id != null && groupProductToPriceMap.containsKey(qli.Product2Id)) {
                        groupWrapper.productPriceMap.put(qli.Product2Id, groupProductToPriceMap.get(qli.Product2Id));
                    }

                    // 记录第一个产品的Product_Group__c作为当前产品组的ID
                    if (productGroupId == null && qli.Product_Group__c != null) {
                        productGroupId = qli.Product_Group__c;
                    }
                }
                
                // 处理阶梯报价
                groupWrapper.ladderLines = new List<Ladder_Line__c>();
                for (QuoteLineItem qli : itemsByQuotationMethod.get(quotationMethodId)) {
                    if (qli.Product_Group__c != null && ladderLinesByLadderDiscount.containsKey(qli.Product_Group__c)) {
                        List<Ladder_Line__c> lines = ladderLinesByLadderDiscount.get(qli.Product_Group__c);
                        groupWrapper.ladderLines = lines;
                        
                        // 转换为页面需要的阶梯数据格式
                        for (Ladder_Line__c line : lines) {
                            Map<String, Object> tier = new Map<String, Object>();
                            tier.put('id', line.Id);
                            tier.put('lowerBound', line.Down_Limit__c);
                            tier.put('upperBound', line.Up_Limit__c);
                            tier.put('unit', line.Unit__c);
                            tier.put('discount', line.Discount__c);
                            tier.put('calculationMethod', line.Calculation_Method__c);
                            groupWrapper.tiers.add(tier);
                            
                            // 根据阶梯行的单位和计算方式判断阶梯类型
                            // if (line.Unit__c == '金额' && line.Calculation_Method__c == '分区') {
                            //     groupWrapper.hasSharedLadderAmountDiscountZone = true;
                            // } else if (line.Unit__c == '金额' && line.Calculation_Method__c == '落区') {
                            //     groupWrapper.hasSharedLadderAmountDiscountDown = true;
                            // } else if (line.Unit__c == '用量' && line.Calculation_Method__c == '落区') {
                            //     groupWrapper.hasSharedLadderUsagePriceDown = true;
                            // }
                        }
                        
                        break; // 我们只需要处理一次阶梯报价
                    }
                }
                
                // 查找各种报价方式并设置标志
                // 1. 保底金额+共享阶梯金额折扣落区
                for (QuoteLineItem qli : itemsByQuotationMethod.get(quotationMethodId)) {
                    if (groupMinimumAmountQuotations.containsKey(qli.Product_Group__c)) {
                        Quotation_Method__c minimumAmountQuote = groupMinimumAmountQuotations.get(qli.Product_Group__c);
                        groupWrapper.minimumAmountQuote = minimumAmountQuote;
                        groupWrapper.hasMinAmountSharedLadderAmountDiscountDown = true;
                        groupWrapper.minimumAmount = String.valueOf(minimumAmountQuote.GuaranteedMin_Amount__c);
                        break; // 只需要处理一次
                    }
                }
                
                // 2. 保底单价*保底数量+共享阶梯用量单价落区
                for (QuoteLineItem qli : itemsByQuotationMethod.get(quotationMethodId)) {
                    if (groupMinimumUnitPriceQuotations.containsKey(qli.Product_Group__c)) {
                        Quotation_Method__c minimumUnitPriceQuote = groupMinimumUnitPriceQuotations.get(qli.Product_Group__c);
                        groupWrapper.hasMinUnitPriceSharedLadderUsagePriceDown = true;
                        groupWrapper.minimumUnitPrice = String.valueOf(minimumUnitPriceQuote.Minimum_ProdUnitPrice__c);
                        groupWrapper.minimumQuantity = String.valueOf(minimumUnitPriceQuote.Minimum_Amout__c);
                        break; // 只需要处理一次
                    }
                }
                
                // 3. 阶梯用量
                for (QuoteLineItem qli : itemsByQuotationMethod.get(quotationMethodId)) {
                    if (groupUsageLadderQuotations.containsKey(qli.Product_Group__c)) {
                        Quotation_Method__c UsageLadderQuote = groupUsageLadderQuotations.get(qli.Product_Group__c);
                        groupWrapper.hasUsageLadderQuote = true;
                        // groupWrapper.fixedUsage = String.valueOf(fixedAmountQuote.Fixed_Dosage__c);
                        // groupWrapper.fixedAmount = String.valueOf(fixedAmountQuote.Fixed_UnitPrice__c);
                        break; // 只需要处理一次
                    }
                }
                
                // 4. 产品折扣
                for (QuoteLineItem qli : itemsByQuotationMethod.get(quotationMethodId)) {
                    if (groupProductDiscountQuotations.containsKey(qli.Product_Group__c)) {
                        Quotation_Method__c productDiscountQuote = groupProductDiscountQuotations.get(qli.Product_Group__c);
                        groupWrapper.hasProductDiscountQuote = true;
                        groupWrapper.discountCoefficient = String.valueOf(productDiscountQuote.Discount_Factor__c);
                        groupWrapper.fixedRebate = String.valueOf(productDiscountQuote.Fixed_Rebate__c);
                        groupWrapper.cashReduce = String.valueOf(productDiscountQuote.Cash_Reduce__c);
                        groupWrapper.credit = String.valueOf(productDiscountQuote.Credit__c);
                        break; // 只需要处理一次
                    }
                }
                // 5.单价*数量
                for (QuoteLineItem qli : itemsByQuotationMethod.get(quotationMethodId)) {
                    if (groupUnitAmountQuotations.containsKey(qli.Product_Group__c)) {
                        Quotation_Method__c unitAmountQuote = groupUnitAmountQuotations.get(qli.Product_Group__c);
                        groupWrapper.hasUnitAmountQuote = true;
                        groupWrapper.unitPrice = String.valueOf(unitAmountQuote.Minimum_ProdUnitPrice__c);
                        groupWrapper.quantity = String.valueOf(unitAmountQuote.Minimum_Amout__c);
                        break; // 只需要处理一次
                    }
                }
                
                // 5、6、7. 纯阶梯报价方式 - 由阶梯行类型处理（已在上面处理）
                
                productGroups.add(groupWrapper);
            }
        } catch (Exception e) {
            ApexPages.addMessage(new ApexPages.Message(
                ApexPages.Severity.ERROR, 
                '加载产品组数据出错: ' + e.getMessage()
            ));
        }
    }
    
    /**
     * 获取报价方式
     * @param quoteLineItems 报价项列表
     * @return 按报价项ID分组的报价方式列表
     */
    private Map<Id, List<Quotation_Method__c>> getPricingMethods(List<QuoteLineItem> quoteLineItems) {
        Map<Id, List<Quotation_Method__c>> result = new Map<Id, List<Quotation_Method__c>>();
        
        if (quoteLineItems.isEmpty()) {
            return result;
        }
        
        Set<Id> qliIds = new Set<Id>();
        for (QuoteLineItem qli : quoteLineItems) {
            qliIds.add(qli.Id);
        }
        
        // 查询报价方式
        List<Quotation_Method__c> pricingMethods = [
            SELECT Id, Quote_Line_Item_ID__c, Method__c, 
                   GuaranteedMin_Amount__c, Minimum_ProdUnitPrice__c, Minimum_Amout__c,
                   Fixed_Dosage__c, Fixed_UnitPrice__c, FixAmount__c, Amount_Cap__c,
                   Discount_Factor__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c
            FROM Quotation_Method__c
            WHERE Quote_Line_Item_ID__c IN :qliIds AND ISGROUP__c = false
            ORDER BY CreatedDate
        ];
        
        // 按报价项ID分组
        for (Quotation_Method__c pm : pricingMethods) {
            if (!result.containsKey(pm.Quote_Line_Item_ID__c)) {
                result.put(pm.Quote_Line_Item_ID__c, new List<Quotation_Method__c>());
            }
            result.get(pm.Quote_Line_Item_ID__c).add(pm);
        }
        
        return result;
    }
    
    // 包装类，用于封装报价行项目及其报价方式
    public class QuoteLineItemWrapper {
        public QuoteLineItem quoteLineItem { get; set; }
        public Quotation_Method__c quoteMethod { get; set; }
        public Decimal listPrice { get; set; } // 产品牌价
        public String listPriceCurrency { get; set; } // 牌价币种
    }
    
    // 产品组包装类
    public class ProductGroupWrapper {
        public Integer groupNumber { get; set; }
        public List<QuoteLineItem> products { get; set; }
        public List<Ladder_Line__c> ladderLines { get; set; }
        public Quotation_Method__c minimumAmountQuote { get; set; }
        public Map<Id, ProductPrice__c> productPriceMap { get; set; } // 产品牌价映射

        // 新增标志 - 七种报价方式
        public Boolean hasMinAmountSharedLadderAmountDiscountDown { get; set; } // 保底金额+共享阶梯金额折扣落区
        public String minimumAmount { get; set; } // 保底金额值

        public Boolean hasMinUnitPriceSharedLadderUsagePriceDown { get; set; } // 保底单价*保底数量+共享阶梯用量单价落区
        public String minimumUnitPrice { get; set; } // 保底单价值
        public String minimumQuantity { get; set; } // 保底数量值
        
        public Boolean hasUsageLadderQuote { get; set; } // 固定金额
        // public String fixedUsage { get; set; } // 固定用量
        // public String fixedAmount { get; set; } // 固定单价
        
        public Boolean hasProductDiscountQuote { get; set; } // 产品折扣
        public String discountCoefficient { get; set; } // 折扣系数
        public String fixedRebate { get; set; } // 固定返利
        public String cashReduce { get; set; } // 现金减免
        public String credit { get; set; } // 信用额度

        public Boolean hasUnitAmountQuote { get; set; } // 单价*数量
        public String unitPrice { get; set; } // 单价
        public String quantity { get; set; } // 数量

        
        public Boolean hasSharedLadderAmountDiscountZone { get; set; } // 共享阶梯金额折扣分区
        public Boolean hasSharedLadderAmountDiscountDown { get; set; } // 共享阶梯金额折扣落区
        public Boolean hasSharedLadderUsagePriceDown { get; set; } // 共享阶梯用量单价落区
        
        // 新增阶梯报价行（为了与页面修改匹配）
        public List<Map<String, Object>> tiers { get; set; }
        
        // 构造函数
        public ProductGroupWrapper() {
            this.products = new List<QuoteLineItem>();
            this.ladderLines = new List<Ladder_Line__c>();
            this.tiers = new List<Map<String, Object>>();
            this.productPriceMap = new Map<Id, ProductPrice__c>();

            // 初始化标志
            this.hasMinAmountSharedLadderAmountDiscountDown = false;
            this.hasMinUnitPriceSharedLadderUsagePriceDown = false;
            this.hasUsageLadderQuote = false;
            this.hasProductDiscountQuote = false;
            this.hasSharedLadderAmountDiscountZone = false;
            this.hasSharedLadderAmountDiscountDown = false;
            this.hasSharedLadderUsagePriceDown = false;
            this.hasUnitAmountQuote=false;
        }
    }
}