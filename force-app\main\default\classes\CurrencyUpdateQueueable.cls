public class CurrencyUpdateQueueable implements Queueable, Database.AllowsCallouts {
    public List<Map<String, Object>> records;
    public CurrencyUpdateQueueable(List<Map<String, Object>> upsertRateData){
        this.records = upsertRateData;
    }
    public void execute(QueueableContext context) {
        ExchangeRateManager manager = new ExchangeRateManager(records);
        manager.createDatedConversionRate();
    }
}