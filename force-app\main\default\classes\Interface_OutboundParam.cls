/********************************************************************
* Purpose: 调用外部接口的参数
* Modify Description: 
* 1. 
********************************************************************/
global without sharing class Interface_OutboundParam {
	// 接口名称
    global String interfaceName;
    // 接口请求头
    //格式 ： Content-Type=application/json ; Authorization=Bearer 你的Token
    global String requestHeader; 
    // 请求方式
    //global String httpMethod;
    //endpoint
    global String endpoint;
    //重试次数
    global Integer retryTimes;
    // 接口数据
    global Object dataObject;
    // 接口数据文本
    global String dataString;
	// 接口日志
	global Interface_Log__c calloutLog;
    // 数据Id
    global Set<Id> recordIdSet;
    // 目标对象
    global String targetObject;
    //成功标记 sap 返回的成功标记（ "msgty":"S"）
    //global String SuccessFlag;

    global ReturnResult returnResult;

    global Boolean isSaveLog;

    global Interface_OutboundParam() {
        returnResult = new ReturnResult();
        isSaveLog = true;
        //SuccessFlag = '"msgty":"S"';
    }

    // 返回结果内部类
	global class ReturnResult {
		// 是否成功 MSGTY （S: 成功 E：失败）
        global Boolean success;
        // 返回提示语
        global String message;
        // 返回接口提示语
        global String interfaceMessage;
        
        global ReturnResult () {}
        global ReturnResult (Boolean success, String message) {
			this.success = success;
			this.message = message;
		}

        global ReturnResult (Boolean success, String message, String interfaceMessage) {
            this.success = success;
            this.message = message;
            this.interfaceMessage = interfaceMessage;
        }
    }
}