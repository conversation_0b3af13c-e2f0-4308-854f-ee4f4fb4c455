({
    doInit: function(component, event, helper) {
        // 从URL参数中获取recordId
        var pageRef = component.get("v.pageReference");
        
        // 记录调试信息
        console.log("productInfoWrapper - 初始化");
        console.log("productInfoWrapper - pageRef:", JSON.stringify(pageRef));
        
        // 方法1：从pageReference中获取
        if (pageRef && pageRef.state) {
            var recordId = pageRef.state.c__recordId;
            if (recordId) {
                component.set("v.recordId", recordId);
                console.log("productInfoWrapper - 从pageReference获取到recordId: " + recordId);
            }
        }
        
        // 方法2：从URL参数中获取
        var urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('c__recordId')) {
            var recordId = urlParams.get('c__recordId');
            component.set("v.recordId", recordId);
            console.log("productInfoWrapper - 从URL参数获取到recordId: " + recordId);
        }
        
        // 如果仍然没有recordId，尝试其他方法
        if (!component.get("v.recordId")) {
            console.log("productInfoWrapper - 未找到recordId参数");
        }
    }
}) 