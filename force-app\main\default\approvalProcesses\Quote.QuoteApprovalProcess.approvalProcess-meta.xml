<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Quote.Account_Type__c</field>
                <operation>equals</operation>
                <value>内部结算客户</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>joyce财务总监批</label>
        <name>joyceApproveProcess</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Assigned_Product_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>产品经理批</label>
        <name>ApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>财务经理审批</label>
        <name>FinanceApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Quote.Is_Agent__c</field>
                <operation>equals</operation>
                <value>真</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>业务合作部</label>
        <name>BusinessCooApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Regional_SalesMana__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>区域总监批</label>
        <name>AreaManaApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>(1 and 2) or 3 or 4</booleanFilter>
            <criteriaItems>
                <field>Quote.Profit_Rate__c</field>
                <operation>lessOrEqual</operation>
                <value>5</value>
            </criteriaItems>
            <criteriaItems>
                <field>Quote.Account_Period_Num__c</field>
                <operation>greaterThan</operation>
                <value>30</value>
            </criteriaItems>
            <criteriaItems>
                <field>Quote.Is_Rebate_Voucher__c</field>
                <operation>equals</operation>
                <value>真</value>
            </criteriaItems>
            <criteriaItems>
                <field>Quote.Potential_Cost_Reminder__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
        </entryCriteria>
        <label>产品总监/总经理</label>
        <name>PM_CEOApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <finalApprovalActions>
        <action>
            <name>ActionFieldUpdate2</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>ActionFieldUpdate6</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>ActionFieldUpdate3</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>ActionFieldUpdate7</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>ActionFieldUpdate</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>ActionFieldUpdate5</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>报价单审批</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>0</processOrder>
    <recallActions>
        <action>
            <name>ActionFieldUpdate4</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>ActionFieldUpdate8</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
