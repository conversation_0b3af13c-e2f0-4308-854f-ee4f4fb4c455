<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>ControlledByParent</externalSharingModel>
    <searchLayouts>
        <customTabListAdditionalFields>IDEA.TITLE</customTabListAdditionalFields>
        <customTabListAdditionalFields>IDEA.BODY</customTabListAdditionalFields>
        <customTabListAdditionalFields>COMMUNITY.NAME</customTabListAdditionalFields>
        <customTabListAdditionalFields>IDEA.CREATED_DATE</customTabListAdditionalFields>
        <customTabListAdditionalFields>IDEA.CREATED_BY_NICKNAME</customTabListAdditionalFields>
        <customTabListAdditionalFields>IDEA.NUM_COMMENTS</customTabListAdditionalFields>
        <customTabListAdditionalFields>VOTE.HAS_VOTED</customTabListAdditionalFields>
        <customTabListAdditionalFields>IDEA.CATEGORIES</customTabListAdditionalFields>
        <customTabListAdditionalFields>IDEA.STATUS</customTabListAdditionalFields>
        <customTabListAdditionalFields>IDEA.LAST_COMMENT_DATE</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>IDEA.TITLE</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>COMMUNITY.NAME</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>IDEA.TITLE</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>COMMUNITY.NAME</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>IDEA.TITLE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>IDEA.BODY</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>COMMUNITY.NAME</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>IDEA.NUM_COMMENTS</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>IDEA.CREATED_BY_NICKNAME</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>IDEA.CATEGORIES</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>IDEA.STATUS</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>IDEA.CREATED_DATE</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ControlledByParent</sharingModel>
</CustomObject>
