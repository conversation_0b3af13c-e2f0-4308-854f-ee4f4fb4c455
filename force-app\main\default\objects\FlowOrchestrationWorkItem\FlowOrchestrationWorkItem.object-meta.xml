<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <searchLayouts>
        <lookupDialogsAdditionalFields>RelatedRecord</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Label</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Status</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Assignee</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LastModifiedBy</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LastModifiedDate</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>CreatedDate</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>RelatedRecord</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Label</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Status</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Assignee</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastModifiedBy</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastModifiedDate</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CreatedDate</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>RelatedRecord</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Label</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Status</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Assignee</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedBy</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CreatedDate</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
