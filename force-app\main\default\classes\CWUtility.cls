public with sharing class CWUtility {

    public static String SAP_Auth_Spo = null;
    public static String SAP_UserName_Spo = null;
    public static String SAP_Password_Spo = null;
    public static final String APP_KEY = 'your_app_key';
    static {
        if (isSandbox()) {
            SAP_Auth_Spo = 'https://sandbox.api.example.com/auth';
            SAP_UserName_Spo = 'sandbox_user';
            SAP_Password_Spo = 'sandbox_password';
        } else {
            SAP_Auth_Spo = 'https://api.example.com/auth';
            SAP_UserName_Spo = 'prod_user';
            SAP_Password_Spo = 'prod_password';
        }
    }

    public static String httpPostMethod(String endpoint, String bodyJSONString){
            HttpRequest req = new HttpRequest();
            req.setEndpoint(endpoint);
            req.setMethod('POST');
            req.setbody(bodyJSONString);
            Http http = new Http();
            HttpResponse res = http.send(req);
            if(res.getStatusCode()==200){
                return res.getBody();
            }
            else{
                return res.getStatus();
            }
            
    }

    public class Token_Response {
        public String expires_in;
        public String access_token;
    }

    public static String sendToSAPRet(String rowDataStr, String endpoint) {
        Http http = new Http();
        HttpRequest req = new HttpRequest();
        HTTPResponse res;
        String resb;

        // 获取token
        req.setEndpoint(SAP_Auth_Spo);
        req.setMethod('POST');
        req.setHeader('grant_type', 'password');
        req.setHeader('username', SAP_UserName_Spo);
        req.setHeader('password', SAP_Password_Spo);
        req.setBody('');
        res = http.send(req);
        resb = res.getBody();

        Token_Response tr = (Token_Response) JSON.deserialize(resb, Token_Response.class);
        // 发送接口
        Http http2 = new Http();
        HttpRequest req2 = new HttpRequest();
        HTTPResponse res2;
        String resb2;

        req2.setTimeout(110000);
        req2.setEndpoint(endpoint);
        req2.setMethod('POST');
        req2.setHeader('access_token', tr.access_token);
        req2.setHeader('Content-Type', 'application/json');
        req2.setBody(rowDataStr);
        res2 = http2.send(req2);
        resb2 = res2.getStatus();
        String resb1 = res2.getBody();
        system.debug('=====202======'+resb1);

        return resb2;
    }

    public static String getToken(String randomstr, long timestamp) {
        if (randomstr == null) {
            // randomstr = randomUUID(16);
        }
        System.debug('-------------------randomstr:' + randomstr);
        System.debug('-------------------timestamp:' + timestamp);
        String str = 'appkey=' + APP_KEY + '&timestamp=' + timestamp + '&random_str=' + randomstr + '&key=' + APP_KEY;

        String result = EncodingUtil.convertToHex(Crypto.generateDigest('MD5', Blob.valueOf(str)));
        System.debug('-------------------result:' + result);
        return result;

    }

    public static String getETQData(String rowDataStr, String endpoint) {

        HttpRequest req = new HttpRequest();
        //endPoint 就是请求路径
        req.setEndpoint(endpoint);
        req.setMethod('POST');
        req.setHeader('Content-Type', 'application/json');
        //请求参数
        req.setBody(rowDataStr);

        Http http = new Http();
        HTTPResponse res = http.send(req);

        String responseBody = res.getBody();
        return responseBody;
    }

    public static Boolean isSandbox() {
        //return URL.getSalesforceBaseUrl().getHost().left(2).equalsignorecase('cs') || URL.getSalesforceBaseUrl().toExternalForm().CONTAINS('.cs');
        return [SELECT IsSandbox FROM Organization LIMIT 1].IsSandbox;
    }

    /********************************************************************
    * Purpose: 将收款计划数据转化为Json
    * Param: 合同ID
    * Return: Json String
    ********************************************************************/
    public static String getPlanJosn(String contractId){
        List<CollectionPlanLine__c> planList = [
            SELECT id,CollectionPlan__r.Contract__r.SapContractNo__c,CollectionPlan__r.Contract__r.ContractAutoNo__c,Description__c,Period_StartDate__c,Period_EndDate__c,
            (Select id,Product__r.Name,Product__r.ProductCode,StartDate__c,EndDate__c,DaysCount__c From CollectionPlanLines__r) FROM CollectionPlanLine__c WHERE CollectionPlan__r.Contract__c =:contractId];
        if(!planList.isEmpty()){
            List<CollectionPlanLine> planObjList = new List<CollectionPlanLine>();
            for(CollectionPlanLine__c plan:planList){
                CollectionPlanLine obj = new CollectionPlanLine();
                obj.id = plan.id;
                obj.planName = plan.Description__c;
                obj.contractNo = plan.CollectionPlan__r.Contract__r.ContractAutoNo__c;
                obj.contractSapNo = plan.CollectionPlan__r.Contract__r.SapContractNo__c;
                obj.periodStartDate = DataProcessTool.formatSAPDate(plan.Period_StartDate__c);
                obj.periodEndDate = DataProcessTool.formatSAPDate(plan.Period_EndDate__c);
                for(CollectionPlanLineProduct__c childPlan:plan.CollectionPlanLines__r){
                    CollectionPlanLineProduct childObj = new CollectionPlanLineProduct();
                    childObj.id = childPlan.id;
                    childObj.productName = childPlan.Product__r.Name;
                    childObj.productSapNumber = childPlan.Product__r.ProductCode;
                    childObj.startDate = DataProcessTool.formatSAPDate(childPlan.StartDate__c);
                    childObj.endDate = DataProcessTool.formatSAPDate(childPlan.EndDate__c);
                    childObj.daysCount = String.valueOf(childPlan.DaysCount__c);
                    obj.poductItems.add(childObj);
                }
                planObjList.add(obj);
            }
            return JSON.serialize(planObjList);
        }
        return '';
    }

    /********************************************************************
    * Purpose: 将SAP的账户密码转换为Credentials
    * Return: SAP的Credentials
    ********************************************************************/
    public static String getSAPEncodedCredentials(){
        String credentials = SAP_UserName_Spo + ':' + SAP_Password_Spo;
        String encodedCredentials = EncodingUtil.base64Encode(Blob.valueOf(credentials));
        return encodedCredentials;
    }

    /********************************************************************
    * Purpose: 实例化对象
    * Param: sobjectName 需要实例的对象API
    * Return: 实例的对象
    ********************************************************************/
    public static Sobject instanceSobjectByName(String sobjectName) {
        Schema.SObjectType convertType = Schema.getGlobalDescribe().get(sobjectName);
        return convertType.newSObject(); 
    }

    /********************************************************************
    * Purpose: 新建接口日志
    * Param: logList 接口日志数据
    ********************************************************************/
    public static void insertLog(List<Interface_Log__c> logList){
        // 判断是否有日志数据
        if (logList == null || logList.size() <= 0){
            return;
        }

        // 汇总接口日志的文件
        List<List<Attachment>> attactmentsList = new List<List<Attachment>>();
        // 汇总需要插入的日志数据
        List<Interface_Log__c> insertLogList = new List<Interface_Log__c>();
        for (Interface_Log__c logInfo : logList){
            logInfo.CallTime__c = system.now();
            // 汇总需要新建的文件
            List<Attachment> attachmentList = new List<Attachment>();

            // 请求的头信息
            if (logInfo.RequestHeader__c != null && logInfo.RequestHeader__c.length() >= 100000){
                Attachment sc = CWUtility.generateAttachment(logInfo.RequestHeader__c);
                attachmentList.add(sc);
                logInfo.RequestHeader__c = logInfo.RequestHeader__c.substring(0, 100000);
            }

            // 请求的内容
            if (logInfo.RequestBody__c != null && logInfo.RequestBody__c.length() >= 100000){
                Attachment sc = CWUtility.generateAttachment(logInfo.RequestBody__c);
                attachmentList.add(sc);
                logInfo.RequestBody__c = logInfo.RequestBody__c.substring(0, 100000);
            }

            // 相应的内容
            if (logInfo.ResponseBody__c != null && logInfo.ResponseBody__c.length() >= 100000){
                Attachment sc = CWUtility.generateAttachment(logInfo.ResponseBody__c);
                attachmentList.add(sc);
                logInfo.ResponseBody__c = logInfo.ResponseBody__c.substring(0, 100000);
            }

            // 错误信息
            if (logInfo.ErrorMessage__c != null && logInfo.ErrorMessage__c.length() >= 100000){
                Attachment sc = CWUtility.generateAttachment(logInfo.ErrorMessage__c);
                attachmentList.add(sc);
                logInfo.ErrorMessage__c = logInfo.ErrorMessage__c.substring(0, 100000);
            }

            // 汇总接口日志
            insertLogList.add(logInfo);

            // 汇总接口日志的相关文件
            attactmentsList.add(attachmentList);
        }
        if (insertLogList.size() > 0){
            try {
                // 插入接口日志
                insert insertLogList;

                // 汇总插入的接口日志文件
                List<Attachment> insertAttachmentList = new List<Attachment>();

                // 循环已插入的接口日志
                for (Integer i = 0; i < insertLogList.size(); i++) {
                    Interface_Log__c logInfo = insertLogList[i];
                    System.debug('logInfo.Id***'+logInfo.Id);
                    // 取对应日志的文件数据
                    if (attactmentsList != null && attactmentsList.size() > 0
                        && attactmentsList[i] != null
                        && attactmentsList[i].size() > 0) {
                        List<Attachment> attachmentList = attactmentsList[i];
                        
                        // 赋值文件的主对象Id
                        for (Attachment attachment : attachmentList){
                            attachment.ParentId = logInfo.Id;
                        }
                        insertAttachmentList.addAll(attachmentList);
                    }
                }
                // 插入接口日志文件
                if (insertAttachmentList.size() > 0){
                    insert insertAttachmentList;
                }
            }catch(Exception e) {
                System.debug(LoggingLevel.INFO, '*** e.getMessage(): ' + e.getMessage());
                System.debug(LoggingLevel.INFO, '*** e.getStackTraceString(): ' + e.getStackTraceString());
            }
        }
    }

    /********************************************************************
    * Purpose: 生成文件
    * Param: content 文件里的内容
    * Return: 需要生成的文件
    ********************************************************************/
    public static Attachment generateAttachment(String content) {
        Attachment sc = new Attachment();
        sc.Name = 'fileContent';
        sc.Body = Blob.valueOf(content);
        return sc;
    }

    /********************************************************************
    * Purpose: 通过时间戳生成接口uuid
    * Return: 返回uuid
    ********************************************************************/
    public static String generateUUID() {
        Blob b = Crypto.generateAesKey(128);
        String uuid = EncodingUtil.convertToHex(b);
        return uuid;
    }

     /********************************************************************
    * Purpose: 发送邮件
    * Param: 邮件内容
    * Return: 
    ********************************************************************/
    public static void sendEmailToAdmin(String body){
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        List<String> adminEmails = new List<String>{'<EMAIL>','<EMAIL>','<EMAIL>'}; 
        mail.setToAddresses(adminEmails);
        mail.setSubject('【接口发送监控提醒】同步失败');
        mail.setPlainTextBody(body);
        try {
            Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{mail});
        } catch(Exception e) {
            System.debug('发送邮件失败: ' + e.getMessage());
        }

    }

    public class CollectionPlanLine{
        public String id;
        public String planName;
        public String contractNo;
        public String contractSapNo;
        public String periodStartDate;
        public String periodEndDate;
        public List<CollectionPlanLineProduct> poductItems;
        public CollectionPlanLine(){
            poductItems = new List<CollectionPlanLineProduct>();
        }

    }

    public class CollectionPlanLineProduct{
        public String id;
        public String productName;
        public String productSapNumber;
        public String startDate;
        public String endDate;
        public String daysCount;

    }
    
    
}