global with sharing class Interface_SAPSyncPaymentToSFDC extends Interface_InboundBase{
    public Map<String, Account> accountNo_AccountMap;
    public Map<String, CollectionPlanLine__c> contract_planLineListMap;
    public Map<String, Product2> productCode_ProductMap;
    global override Map<String, Object> execute(Interface_InboundParam param){
        Map<String, Object> resultMap = new Map<String, Object>();
        
        //upsert  records
        try {
            Interface_PaymentObject record = (Interface_PaymentObject)JSON.deserialize(param.dataString, Interface_PaymentObject.class);
            List<BillPaymentAdvice__c> paymentList = new List<BillPaymentAdvice__c>();
            List<BillPaymentAdviceDetail__c> paymentDetailList = new List<BillPaymentAdviceDetail__c>();
            resultMap.put('uuid',record.uuid);

            //获取所有关联的客户ID
            Set<String> accountNo = new Set<String>();
            //Salesforce 合同号
            Set<String> contractNo = new Set<String>();
            //产品编码
            Set<String> productCode = new Set<String>();

            
            for (Interface_PaymentObject.Header header : record.header) {
                if (String.isBlank(header.kunnr)) {
                    throw new DmlException ('客户编号必填'); // 抛出异常
                }
                if (String.isBlank(header.bstkd)) {
                    throw new DmlException ('合同编号必填'); // 抛出异常
                }
                accountNo.add(header.kunnr);
                contractNo.add(header.bstkd);
                if (!header.item.isEmpty()) {
                    for (Interface_PaymentObject.Item item : header.item) {
                        if (String.isNotBlank(item.matnr)) {
                            productCode.add(item.matnr);
                        }
                    }
                }
            }
            //根据合同编码获取系统中合同下的全部收款计划明细
            if (!contractNo.isEmpty()) {
                contract_planLineListMap = getCollectionPlanLineByContractNo(contractNo);
            }
            //根据客户编号获取客户信息
            if (!accountNo.isEmpty()) {
                accountNo_AccountMap = getAccountByAccountNo(accountNo);
            }
            //根据产品编号查找产品
            if (!productCode.isEmpty()) {
                productCode_ProductMap = Service_Product.searchProductByProductCode(productCode);
            }
            
            //处理数据
            for (Interface_PaymentObject.Header header : record.header) {
                paymentList.add(generatePayment(header));
                List<Interface_PaymentObject.Item> items = header.item;
                if (!items.isEmpty()) {
                    paymentDetailList.addAll(generatePaymentDetail(header));
                }
                
            }
            Database.upsert(paymentList,BillPaymentAdvice__c.UniqueKey__c);
            Database.insert(paymentDetailList);

            resultMap.put('msgty','S');
            resultMap.put('msgtx', 'Success');
             
           
        } catch (Exception e) {
            
            resultMap.put('msgty','E');
            resultMap.put('msgtx', e.getMessage());
          
        }
        resultMap.put('msgid', '');
        resultMap.put('msgno', '');
        resultMap.put('sapnum','');
        resultMap.put('field1','');
        resultMap.put('field2','');
        resultMap.put('field3',''); 

        // 设置响应头和状态码
        RestContext.response.addHeader('Content-Type', 'application/json');
        RestContext.response.statusCode = 200;
        RestContext.response.responseBody = Blob.valueOf(JSON.serialize(resultMap));
        return resultMap;
    }

    public BillPaymentAdvice__c generatePayment(Interface_PaymentObject.Header header){
        BillPaymentAdvice__c payment = new BillPaymentAdvice__c();
        payment.RecordTypeId = Schema.getGlobalDescribe()
                     .get('BillPaymentAdvice__c')
                     .getDescribe()
                     .getRecordTypeInfosByDeveloperName()
                     .get('NormalPaymentAdvice')
                     .getRecordTypeId();
        payment.Billing_Number__c = header.vbeln;
        payment.Billing_Type__c = header.fkart;
        payment.Account_Number__c = header.kunnr;//存储接口传的客户编码
        payment.Account__c = accountNo_AccountMap.get(header.kunnr).Id;
        payment.Billing_Date__c = DataProcessTool.parseSAPDate(header.fkdat);
        payment.Creat_Date__c = DataProcessTool.parseSAPDate(header.erdat);
        payment.Company_Code__c = header.bukrs;
        payment.PaymentTerm__c = header.zterm;
        payment.Payment_Currency__c = header.waerk;
        payment.Total_Amount__c = Decimal.valueOf(header.netwr.trim());
        payment.Total_Tax_Amount__c = Decimal.valueOf(header.mwsbk.trim());
        payment.PayableAmount__c = payment.Total_Amount__c+payment.Total_Tax_Amount__c;
        payment.SAP_Contract_Number__c = header.zvbelv;
        payment.Contract_Number__c = header.bstkd;
        payment.Exchange_Rate__c = Decimal.valueOf(header.zexrate.trim());
        payment.Contract_Currency__c = header.zconcur;
        payment.Adjustment_Note__c = header.augru_auft;
        payment.UniqueKey__c = payment.Contract_Number__c+'-'+payment.Creat_Date__c;
        if (!contract_planLineListMap.containsKey(payment.Contract_Number__c+'-'+payment.Creat_Date__c)) {
            throw new DmlException('未查找到关联的收款计划明细');
        }
        payment.Collection_Plan_Line__c = contract_planLineListMap.get(payment.Contract_Number__c+'-'+payment.Creat_Date__c).Id;
        return payment;

    }
    public List<BillPaymentAdviceDetail__c> generatePaymentDetail(Interface_PaymentObject.Header header){
        List<BillPaymentAdviceDetail__c> recordList = new List<BillPaymentAdviceDetail__c>();
        for (Interface_PaymentObject.Item item : header.item) {
            BillPaymentAdviceDetail__c record = new BillPaymentAdviceDetail__c();
            record.Item_Key__c = item.zitem;
            record.Items_No__c = item.posnr;
            record.Product_Code__c = item.matnr;
            if (!String.isBlank(item.matnr) && !productCode_ProductMap.containsKey(record.Product_Code__c)) {
                throw new DmlException('系统中未查找到关联的产品记录');
            }
            if (!String.isBlank(item.matnr) && productCode_ProductMap.containsKey(record.Product_Code__c)) {
                record.Product__c = productCode_ProductMap.get(record.Product_Code__c).Id;
            }
            record.Quantity__c = Decimal.valueOf(item.fkimg.trim());
            record.Unit__c = item.meins;
            record.Total_Amount__c = Decimal.valueOf(item.znetwr.trim());
            record.Tax__c = Decimal.valueOf(item.mwsbp.trim());
            record.Tax_Rate__c = Decimal.valueOf(item.kbetr.trim());
            record.BillPaymentAdvice__r = new BillPaymentAdvice__c(Billing_Number__c=header.vbeln);
            recordList.add(record);
        }
        return recordList;
    }

    public Map<String, CollectionPlanLine__c> getCollectionPlanLineByContractNo(Set<String> contractNoSet){
        Map<String, CollectionPlanLine__c> lineMap = new Map<String, CollectionPlanLine__c>();
        for (CollectionPlanLine__c line : [SELECT Id,CollectionPlan__r.Contract__r.ContractAutoNo__c,
                                     Period_StartDate__c,Period_EndDate__c,PaymentDate__c 
                              FROM CollectionPlanLine__c 
                              WHERE CollectionPlan__r.Contract__r.ContractAutoNo__c IN : contractNoSet]) {
            lineMap.put(line.CollectionPlan__r.Contract__r.ContractAutoNo__c+'-'+line.Period_EndDate__c,line);
            
        }
        return lineMap;
    }
    public Map<String, Account> getAccountByAccountNo(Set<String> accountNoSet){
        Map<String, Account> accountMap = new Map<String, Account>();
        List<Account> accList = [SELECT Id,SAP_Num__c FROM Account WHERE SAP_Num__c IN :accountNoSet];
        if (accList.isEmpty()) {
            throw new DmlException ('系统中未查找到客户'); // 抛出异常
        }
        for (Account acc : accList) {
            accountMap.put(acc.SAP_Num__c, acc);
        }
        return accountMap;
    }
    
}