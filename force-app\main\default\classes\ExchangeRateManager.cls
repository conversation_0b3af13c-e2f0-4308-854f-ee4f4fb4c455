public class ExchangeRateManager {
    public Interface_OutboundParam param {get;set;}
    Map<String, Object> requestBody = new Map<String, Object>();
    public List<Map<String, Object>> records;
    public Interface_Log__c logInfo {get;set;}
    public ExchangeRateManager(List<Map<String, Object>> upsertRateData){
        this.records = upsertRateData;
    }
    public void createDatedConversionRate() {
        Integer timeout = 120000;
        String baseUrl = URL.getOrgDomainUrl().toExternalForm(); 
        String endpoint =baseUrl + '/services/data/v56.0/composite/tree/DatedConversionRate';
        Integer retryTime = 3;
        String targetObject = 'DatedConversionRate';
        
        //String token = '';
        String requestHeader = getRequestHeader();
        Map<String, Object> requestBody = new Map<String, Object>{
            'records' => records
        };
        String requestBodyStr = JSON.serialize(requestBody);
        Http http = new Http();
        HttpRequest httpRequest = new HttpRequest();
        httpRequest.setHeader('Content-Type', 'application/json');
        httpRequest.setHeader('Authorization', 'Bearer ' + UserInfo.getSessionId());
        httpRequest.setMethod('POST');
        httpRequest.setTimeout(timeout);
        httpRequest.setEndpoint(endpoint); 
        httpRequest.setBody(requestBodyStr);
        logInfo = new Interface_Log__c();
        logInfo.InterfaceName__c = 'ExchangeRateManager';
        logInfo.CallTime__c = System.now();
        logInfo.InterfaceType__c = 'outbound';
        logInfo.Endpoint__c = endpoint;
        logInfo.RequestHeader__c = requestHeader;
        logInfo.RequestBody__c = requestBodyStr;
        HttpResponse res = http.send(httpRequest);
        if (res.getStatusCode() != 200) {
            logInfo.Status__c = 'Failed';
            logInfo.ErrorMessage__c = res.getBody();
            insert logInfo;
        }
        
    }

    public String getRequestHeader(){
        Map<String, String> headerMap = new Map<String, String>();
        headerMap.put('Content-Type', 'application/json');
        headerMap.put('Authorization', 'Bearer ' + UserInfo.getSessionId());
        headerMap.put('httpMethod','PATCH');
        return JSON.serialize(headerMap);
    }
}