@RestResource(urlMapping='/SyncProductPrice/*')
global with sharing class Interface_SAPSyncProductPriceToSFDCRest {
    
    @HttpPost
    global static void Interface_SAPSyncProductToSFDCRest() {
        RestRequest req = RestContext.request;
        Interface_Log__c log = new Interface_Log__c();
        
        // 1. 解析请求数据
        Interface_InboundParam params = new Interface_InboundParam();
        params.requestHeader = req.headers.toString();
        params.dataString =  req.requestBody.toString();
        System.debug(params.dataString);
        params.interfaceName = 'Interface_SAPSyncProductToSFDCRest';
        params.executeInboundClassName = 'Interface_SAPSyncProductPriceToSFDC';
        Interface_InboundExecutor executor= new Interface_InboundExecutor(params,log);
        executor.execute();  
    }
}