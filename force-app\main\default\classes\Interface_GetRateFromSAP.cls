public without sharing class Interface_GetRateFromSAP {
    public String requestDataStr;
    public Interface_OutboundParam param {get;set;}
    Map<String, Object> requestBody = new Map<String, Object>();

    public Interface_GetRateFromSAP(String requestDataStr) {
        this.requestDataStr = requestDataStr;
        param = new Interface_OutboundParam();
        param.interfaceName = 'Interface_GetRateFromSAP';
        param.endpoint ='callout:SAP_API_Credential';
        param.retryTimes = 3;
        param.targetObject = 'DatedConversionRate';
        //param.httpMethod = 'POST';
        
        //String token = '';
        param.requestHeader = getRequestHeader();
        param.dataString = getRequestBody();

        Interface_OutboundExecutor getRate = new Interface_OutboundExecutor(param);
        String responseBody = getRate.execute();
        System.debug(responseBody);
        if (!String.isEmpty(responseBody)) {
            Interface_GetRateFromSAP.RateRes responseObject = (Interface_GetRateFromSAP.RateRes)JSON.deserialize(responseBody, Interface_GetRateFromSAP.RateRes.class);
            List<Map<String, Object>> records = new List<Map<String, Object>>();
            if (responseObject.MSGTY == 'S') {
                List<Interface_GetRateFromSAP.RateItem> rateList = (List<Interface_GetRateFromSAP.RateItem>)JSON.deserialize(responseObject.field1, List<Interface_GetRateFromSAP.RateItem>.class);
                for (Interface_GetRateFromSAP.RateItem item : rateList) {
                    System.debug(item.TCURR);
                    System.debug(item.UKURS);
                    System.debug(DataProcessTool.parseSAPDate(item.GDATU));
                    records.add(createRateRecord(item.TCURR, item.UKURS.trim(),DataProcessTool.parseSAPDate(item.GDATU)));
                    //createRateRecord(item.TCURR,item.UKURS.trim(),DataProcessTool.parseSAPDate(item.GDATU));
                }

            }
            if (!records.isEmpty()) {
                System.enqueueJob(new CurrencyUpdateQueueable(records));
            }
        }
        
    }

    public String getRequestHeader(){
        String encodedCredentials = CWUtility.getSAPEncodedCredentials();
        Map<String, String> headerMap = new Map<String, String>();
        headerMap.put('Content-Type', 'application/json');
        headerMap.put('sap-client', '100');
        List<String> headerList = new List<String>();
        for (String key : headerMap.keySet()) {
            headerList.add(key + '=' + headerMap.get(key));
        }
        String headerString = String.join(headerList, ';');
        return headerString;
    }

    public String getRequestBody(){
        Interface_GetRateFromSAP.Interface_ExchangeRateRequest reqBody = new Interface_GetRateFromSAP.Interface_ExchangeRateRequest();
        Interface_GetRateFromSAP.ReqItem req = new Interface_GetRateFromSAP.ReqItem();
        req.KURST = 'M';
        req.FCURR = 'SGD';
        req.TCURR = '';
        req.GDATU = this.requestDataStr;//TODO: 需要根据日期动态获取
        reqBody.req = req;
        String reqString = JSON.serialize(reqBody);
        return reqString;
    }
    

    //Resquest Object
    public class Interface_ExchangeRateRequest {
        //Request Object
        public String UUID;//接口唯一标识
        public String ZNUMB;//接口编号
        public String FSYSID;//请求系统
        public reqItem req;
        
        public Interface_ExchangeRateRequest(){
            UUID = CWUtility.generateUUID();
            ZNUMB = 'FI005';
            FSYSID = 'SALESFORCE';
        }
    }
    public class ReqItem{
        public String KURST;//汇率类型
        public String FCURR;//源货币
        public String TCURR;//目标货币
        public String GDATU;//日期
    }
   

    //Response Object
    public class RateRes{

        public String MSGTY;
        public String MSGTX;
        public String MSGID;
        public String MSGNO;
        public String UUID;
        public String SAPNUM;
        public String field1;
        //public List<RateItem> field1;
        public String field2;
        public String field3;
    }

    public class RateItem{
        public String KURST;//汇率类型
        public String FCURR;//源货币
        public String TCURR;//目标货币
        public String GDATU;//货币汇率生效日期
        public String UKURS;//汇率(1 CNY = 0.19 USD)
        public String FFACT;//兑换比率（Ratio for the "from" currency units）
        public String TFACT;//兑换比率  (Ratio for the "to" currency units)
    }

    // 创建单个汇率记录结构
    public static Map<String, Object> createRateRecord(String isoCode, String rateStr, Date effectiveDate) {
        
        Decimal rate = convertToDecimal(rateStr);
        Decimal formattedRate = rate.setScale(6, System.RoundingMode.HALF_UP);
        return new Map<String, Object>{
            'attributes' => new Map<String, String>{
                'type' => 'DatedConversionRate',
                'referenceId' => isoCode+effectiveDate},
            'IsoCode' => isoCode,
            'ConversionRate' => formattedRate,
            'StartDate' => effectiveDate
        };
    }
    public static Decimal convertToDecimal(String value) {
        return Decimal.valueOf(value);
    }


}