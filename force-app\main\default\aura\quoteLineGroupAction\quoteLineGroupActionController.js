({
    doInit : function(component, event, helper) {
        // 获取记录ID
        var recordId = component.get("v.recordId");
        console.log('QuoteLineGroupAction - 初始化 - 记录ID:', recordId);
        
        // 使用标准的Lightning Navigation Service
        var navService = component.find("navService");
        
        // 方法1：直接导航到自定义标签页
        var pageReference = {
            type: 'standard__navItemPage',
            attributes: {
                apiName: 'Edit_Quote_Lines'
            },
            state: {
                c__recordId: recordId,
                c__startInEditMode: 'true' // 确保传递编辑模式参数
            }
        };
        
        console.log('QuoteLineGroupAction - 尝试导航方法1');
        
        // 在新窗口中打开
        navService.generateUrl(pageReference)
            .then($A.getCallback(function(url) {
                console.log('QuoteLineGroupAction - 导航URL (方法1):', url);
                window.open(url, '_self');
                
                // 关闭当前快速操作
                $A.get("e.force:closeQuickAction").fire();
            }))
            .catch($A.getCallback(function(error) {
                console.error('导航错误 (方法1): ', error);
                
                // 如果方法1失败，尝试方法2：使用webPage类型和组件定义
                try {
                    console.log('QuoteLineGroupAction - 尝试导航方法2');
                    var compDefinition = {
                        componentDef: "c:quoteLineGroupCreate",
                        attributes: {
                            recordId: recordId,
                            startInEditMode: true // 确保设置为true
                        }
                    };
                    
                    // Base64编码组件定义
                    var encodedCompDef = btoa(JSON.stringify(compDefinition));
                    
                    var webPageReference = {
                        type: 'standard__webPage',
                        attributes: {
                            url: '/one/one.app#' + encodedCompDef
                        }
                    };
                    
                    navService.generateUrl(webPageReference)
                        .then($A.getCallback(function(webUrl) {
                            console.log('QuoteLineGroupAction - 导航URL (方法2):', webUrl);
                            window.open(webUrl, '_blank');
                            
                            // 关闭当前快速操作
                            $A.get("e.force:closeQuickAction").fire();
                        }))
                        .catch($A.getCallback(function(webError) {
                            console.error('导航错误 (方法2): ', webError);
                            
                            // 如果方法2也失败，使用备用方法
                            console.log('QuoteLineGroupAction - 尝试备用导航方法');
                            var fallbackUrl = "/lightning/n/Edit_Quote_Lines?c__recordId=" + encodeURIComponent(recordId) + "&c__startInEditMode=true";
                            console.log('QuoteLineGroupAction - 备用导航URL:', fallbackUrl);
                            window.open(fallbackUrl, "_blank");
                            
                            // 关闭当前快速操作
                            $A.get("e.force:closeQuickAction").fire();
                        }));
                } catch(e) {
                    console.error('编码错误: ', e);
                    
                    // 如果备用方法失败，使用最简单的方法
                    console.log('QuoteLineGroupAction - 尝试最终备用导航方法');
                    var fallbackUrl = "/lightning/n/Edit_Quote_Lines?c__recordId=" + encodeURIComponent(recordId) + "&c__startInEditMode=true";
                    console.log('QuoteLineGroupAction - 最终备用导航URL:', fallbackUrl);
                    window.open(fallbackUrl, "_blank");
                    
                    // 关闭当前快速操作
                    $A.get("e.force:closeQuickAction").fire();
                }
            }));
    }
}) 