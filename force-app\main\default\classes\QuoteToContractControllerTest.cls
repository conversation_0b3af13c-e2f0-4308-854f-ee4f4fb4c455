@isTest
public class QuoteToContractControllerTest {
    @isTest
    static void testConvertToContract_success() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;
        Contact con = new Contact(LastName = 'Test Contact', AccountId = acc.Id);
        insert con;
        Product2 prod = new Product2(Name = 'Test Product', IsActive = true);
        insert prod;
        Pricebook2 pb = new Pricebook2(Name = 'Standard', IsActive = true);
        insert pb;
        PricebookEntry pbe = new PricebookEntry(Product2Id = prod.Id, Pricebook2Id = pb.Id, UnitPrice = 100, IsActive = true);
        insert pbe;
        Opportunity opp = new Opportunity(Name = 'Test Opp', StageName = 'Prospecting', CloseDate = Date.today(), AccountId = acc.Id);
        insert opp;
        Quote quote = new Quote(Name = 'Test Quote', OpportunityId = opp.Id, Contract_Cur__c = 'USD', Settlement_Cur__c = 'USD', Add_Type__c = '新合同' );
        insert quote;
        QuoteLineItem qli = new QuoteLineItem(QuoteId = quote.Id, Product2Id = prod.Id, UnitPrice = 100, Quantity = 1, PricebookEntryId = pbe.Id);
        insert qli;
        quote = [SELECT Id, Name, Contract_Cur__c, Settlement_Cur__c, Add_Type__c, (SELECT Id, Product2Id, UnitPrice, Quantity, PricebookEntryId, Product2.Name FROM QuoteLineItems) FROM Quote WHERE Id = :quote.Id];
        Test.startTest();
        ApexPages.StandardController sc = new ApexPages.StandardController(quote);
        QuoteToContractController ctrl = new QuoteToContractController(sc);
        PageReference pr = ctrl.convertToContract();
        Test.stopTest();
        System.assertNotEquals(null, pr, 'PageReference should not be null');
    }

    @isTest
    static void testConvertToContract_noLineItems() {
        Account acc = new Account(Name = 'Test Account2');
        insert acc;
        Opportunity opp = new Opportunity(Name = 'Test Opp2', StageName = 'Prospecting', CloseDate = Date.today(), AccountId = acc.Id);
        insert opp;
        Quote quote = new Quote(Name = 'Test Quote2', OpportunityId = opp.Id, Contract_Cur__c = 'USD', Settlement_Cur__c = 'USD', Add_Type__c = '新合同');
        insert quote;
        quote = [SELECT Id, Name, Contract_Cur__c, Settlement_Cur__c, Add_Type__c, (SELECT Id, Product2Id, UnitPrice, Quantity, PricebookEntryId, Product2.Name FROM QuoteLineItems) FROM Quote WHERE Id = :quote.Id];
        Test.startTest();
        ApexPages.StandardController sc = new ApexPages.StandardController(quote);
        QuoteToContractController ctrl = new QuoteToContractController(sc);
        PageReference pr = ctrl.convertToContract();
        Test.stopTest();
        System.assertNotEquals(null, pr, 'PageReference should not be null even if no line items');
    }

    @isTest
    static void testConvertToContract_exception() {
        Account acc = new Account(Name = 'Test Account3');
        insert acc;
        Opportunity opp = new Opportunity(Name = 'Test Opp3', StageName = 'Prospecting', CloseDate = Date.today(), AccountId = acc.Id);
        insert opp;
        Quote quote = new Quote(Name = 'Test Quote3', OpportunityId = opp.Id, Contract_Cur__c = 'USD', Settlement_Cur__c = 'USD', Add_Type__c = '不存在的类型');
        insert quote;
        quote = [SELECT Id, Name, Contract_Cur__c, Settlement_Cur__c, Add_Type__c, (SELECT Id, Product2Id, UnitPrice, Quantity, PricebookEntryId, Product2.Name FROM QuoteLineItems) FROM Quote WHERE Id = :quote.Id];
        Test.startTest();
        ApexPages.StandardController sc = new ApexPages.StandardController(quote);
        QuoteToContractController ctrl = new QuoteToContractController(sc);
        PageReference pr = ctrl.convertToContract();
        Test.stopTest();
        System.assertEquals(null, pr, 'PageReference should be null if exception occurs');
    }
}