global class Interface_SAPSyncProductToSFDC extends Interface_InboundBase{
    
    global override Map<String, Object> execute(Interface_InboundParam param){
    
       
        Map<String, Object> resultMap = new Map<String, Object>();
        
        List<Product2> insertProductList = new List<Product2>();
        //TODO: 拿到数据后的具体操作从这里进行
        List<Interface_ProductAndHierarchyObject.ProductItem> productItemList = new List<Interface_ProductAndHierarchyObject.ProductItem>();
        Interface_ProductAndHierarchyObject productRecord = (Interface_ProductAndHierarchyObject)JSON.deserialize(param.dataString, Interface_ProductAndHierarchyObject.class);
        resultMap.put('uuid',productRecord.uuid);
        
        productItemList = productRecord.item;
        Map<String, Product2> parentProduct1 = new Map<String, Product2>();
        Map<String, Product2> parentProduct2 = new Map<String, Product2>();
        Map<String, Product2> parentProduct3 = new Map<String, Product2>();
        Map<String, Product2> parentProduct4 = new Map<String, Product2>();
        Map<String, Product2> parentProduct5 = new Map<String, Product2>();
        for (Interface_ProductAndHierarchyObject.ProductItem item : productItemList) {
            if (!parentProduct1.containsKey(item.ZPRODH1)) {
                parentProduct1.put(item.ZPRODH1, new Product2( Name = item.VTEXT1,LevelCode__c = item.ZPRODH1,Level__c = '1'));
            }
            if (!String.isBlank(item.ZPRODH2) && !parentProduct2.containsKey(item.ZPRODH2)) {
                parentProduct2.put(item.ZPRODH2, new Product2( Name = item.VTEXT2,LevelCode__c = item.ZPRODH2,Level__c = '2',parentProduct__r = new Product2(ProductCode = item.ZPRODH1)));
            }
            if (!String.isBlank(item.ZPRODH3) && !parentProduct3.containsKey(item.ZPRODH3)) {
                parentProduct3.put(item.ZPRODH3, new Product2( Name = item.VTEXT3,LevelCode__c = item.ZPRODH3,Level__c = '3',parentProduct__r = new Product2(ProductCode = item.ZPRODH2)));
            }
            if (!String.isBlank(item.ZPRODH4) && !parentProduct4.containsKey(item.ZPRODH4)) {
                parentProduct4.put(item.ZPRODH4, new Product2( Name = item.VTEXT4,LevelCode__c = item.ZPRODH4,Level__c = '4',parentProduct__r = new Product2(ProductCode = item.ZPRODH3)));
            }
            if (!String.isBlank(item.ZPRODH5) && !parentProduct5.containsKey(item.ZPRODH5)) {
                parentProduct5.put(item.ZPRODH5, new Product2( Name = item.VTEXT5,LevelCode__c = item.ZPRODH5,Level__c = '5',parentProduct__r = new Product2(ProductCode = item.ZPRODH4)));
            }
            insertProductList.add(generateProduct(item));
        }

        for (Interface_ProductAndHierarchyObject.ProductItem item : productItemList) {
            if (parentProduct1.containsKey(item.PRDHA)) {
                parentProduct1.remove(item.PRDHA);
            }
            if (parentProduct2.containsKey(item.PRDHA)) {
                parentProduct2.remove(item.PRDHA);
            }
            if (parentProduct3.containsKey(item.PRDHA)) {
                parentProduct3.remove(item.PRDHA);
            }
            if (parentProduct4.containsKey(item.PRDHA)) {
                parentProduct4.remove(item.PRDHA);
            }
            if (parentProduct5.containsKey(item.PRDHA)) {
                parentProduct5.remove(item.PRDHA);
            }
        }
        //upsert product records
        try {
            if(!parentProduct1.isEmpty()) Database.upsert(parentProduct1.values(),Product2.Fields.LevelCode__c);
            if(!parentProduct2.isEmpty()) Database.upsert(parentProduct2.values(),Product2.Fields.LevelCode__c);
            if(!parentProduct3.isEmpty()) Database.upsert(parentProduct3.values(),Product2.Fields.LevelCode__c);
            if(!parentProduct4.isEmpty()) Database.upsert(parentProduct4.values(),Product2.Fields.LevelCode__c);
            if(!parentProduct5.isEmpty()) Database.upsert(parentProduct5.values(),Product2.Fields.LevelCode__c);
            if(!insertProductList.isEmpty()) Database.upsert(insertProductList, Product2.Fields.LevelCode__c) ;

            resultMap.put('msgty','S');
            resultMap.put('msgtx', 'Success');
             
            //send up
        } catch (Exception e) {
            
            resultMap.put('msgty','E');
            resultMap.put('msgtx', e.getMessage());
          
        }
        resultMap.put('msgid', '');
        resultMap.put('msgno', '');
        resultMap.put('sapnum','');
        resultMap.put('field1','');
        resultMap.put('field2','');
        resultMap.put('field3',''); 

        // 设置响应头和状态码
        RestContext.response.addHeader('Content-Type', 'application/json');
        RestContext.response.statusCode = 200;
        RestContext.response.responseBody = Blob.valueOf(JSON.serialize(resultMap));
        return resultMap;
    }

    private Product2 generateProduct(Interface_ProductAndHierarchyObject.ProductItem item){
   
        Product2 product = new Product2();
        product.Material_Type__c = item.MTART;
        product.ProductCode = item.MATNR;
        product.Name = item.MAKTX;
        product.Description = item.MAKTX;
        product.English_Description__c = item.MAKTX_EN;
        product.QuantityUnitOfMeasure = item.MEINS;
        product.Material_Group__c = item.MATKL;
        product.Family = item.SPART;
        product.IsActive = true;
        product.LevelCode__c = item.PRDHA;
        product.Level__c = item.STUFE;
        product.LevelCode1__c = item.ZPRODH1;
        product.LevelCode2__c = item.ZPRODH2;
        product.LevelCode3__c = item.ZPRODH4;
        product.LevelCode4__c = item.ZPRODH5;
        product.LevelCode5__c = item.ZPRODH5;
        if (product.Level__c == '2') {
            product.parentProduct__r = new Product2(LevelCode__c = item.ZPRODH1);
        }
        if (product.Level__c == '3') {
            product.parentProduct__r = new Product2(LevelCode__c = item.ZPRODH2);
        }
        if (product.Level__c == '4') {
            product.parentProduct__r = new Product2(LevelCode__c = item.ZPRODH3);
        }
        if (product.Level__c == '5') {
            product.parentProduct__r = new Product2(LevelCode__c = item.ZPRODH4);
        }
        
        return product;
        
    }
}