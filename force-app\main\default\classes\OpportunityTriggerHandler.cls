public with sharing class Opportunity<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends Tri<PERSON><PERSON>andler {
    public OpportunityTriggerHandler() {
        super('Opportunity');
    }
    
    public override void doAfterUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        List<Opportunity> newOpportunities = (List<Opportunity>)newList;
        Map<Id, Opportunity> oldOpportunities = (Map<Id, Opportunity>)oldMap;
        
        // 处理审批通过的机会，创建销售状态记录并同步账户到SAP
        handleApprovedOpportunities(newOpportunities, oldOpportunities);
    }

    // ================== override方法结束，自定义方法开始 ==================
    
    /**
     * 处理审批通过的机会
     * 1. 对于没有销售状态记录的客户，创建销售状态记录
     * 2. 根据客户SAP_Num__c字段是否为空，同步客户信息到SAP
     */
    private void handleApprovedOpportunities(List<Opportunity> newOpportunities, Map<Id, Opportunity> oldOpportunities) {
        Set<Id> accountIdsToProcess = new Set<Id>();
        Map<Id, Opportunity> approvedOpportunities = new Map<Id, Opportunity>();
        
        // 筛选出状态变为已审批的机会
        for(Opportunity newOppo : newOpportunities) {
            Opportunity oldOppo = oldOpportunities.get(newOppo.Id);
            System.debug(newOppo.ApprovalStatus__c+'----'+ oldOppo.ApprovalStatus__c + '---'+newOppo.Is_Test__c);
            if(newOppo.ApprovalStatus__c != oldOppo.ApprovalStatus__c && newOppo.ApprovalStatus__c == 'Approved' && newOppo.Is_Test__c) {
                accountIdsToProcess.add(newOppo.AccountId);
                approvedOpportunities.put(newOppo.Id, newOppo);
            }
        }
        System.debug('accountIdsToProcess'+ accountIdsToProcess.size());
        if(accountIdsToProcess.isEmpty()) {
            return;
        }
        
        // 查询相关客户
        Map<Id, Account> accountMap = new Map<Id, Account>([
            SELECT Id, SAP_Num__c, 
            (SELECT Id, Product_Line__c, Sales_Status__c FROM ChildAccounts__r LIMIT 1) 
            FROM Account 
            WHERE Id IN :accountIdsToProcess
        ]);
        
        // 批量处理销售状态记录创建和SAP同步
        createSalesStatusRecordsAndSyncToSAP(approvedOpportunities, accountMap);
    }
    
    /**
     * 批量创建销售状态记录并同步到SAP
     */
    private void createSalesStatusRecordsAndSyncToSAP(Map<Id, Opportunity> approvedOpportunities, Map<Id, Account> accountMap) {
        List<Sales_Status__c> salesStatusToInsert = new List<Sales_Status__c>();
        Map<Id,String> createAccountIdMap = new Map<Id,String>();
        // Map<Id,String> updateAccountIds = new Map<Id,String>();
        String productLine;
        
        // 准备批量插入的销售状态记录和SAP同步数据
        System.debug('approvedOpportunities***:'+approvedOpportunities.values().size());
        for(Opportunity opportunity : approvedOpportunities.values()) {
            Account account = accountMap.get(opportunity.AccountId);
            
            if(account != null && account.ChildAccounts__r.size() == 0) {
                // 准备销售状态记录
                Sales_Status__c salesStatus = new Sales_Status__c();
                salesStatus.Account__c = opportunity.AccountId;
                salesStatus.Product_Line__c = opportunity.Primary_Product__c;
                salesStatus.Sales_Status__c = '01';
                salesStatus.SalesPerson__c = opportunity.SalesPerson__c;
                salesStatus.Product_Group_Control__c = opportunity.Product_Group_Control__c;
                salesStatusToInsert.add(salesStatus);
                
                // 存储产品线信息，用于SAP同步
                productLine = opportunity.Primary_Product__c;
                
                // 根据SAP_Num__c判断同步类型
                // if(String.isEmpty(account.SAP_Num__c)) {
                    createAccountIdMap.put(account.Id, productLine);
                // } else {
                //     updateAccountIds.put(account.Id, productLine);
                // } 
            }
        }
        
        // 批量插入销售状态记录
        System.debug('salesStatusToInsert***:'+salesStatusToInsert.size());
        if(!salesStatusToInsert.isEmpty()) {
            insert salesStatusToInsert;
        }
        
        // 批量同步到SAP
        System.debug('createAccountIdMap***'+createAccountIdMap.keySet().size());
        if(createAccountIdMap.keySet().size()>0) {
            Interface_CRMSyncAccountToSAP.doSyncAccountToSAP(createAccountIdMap);
        }
        
        // if(updateAccountIds.keySet().size()>0) {
        //     Interface_CRMSyncAccountToSAP.doSyncAccountToSAP(updateAccountIds, 'update');
        // }
    }
}