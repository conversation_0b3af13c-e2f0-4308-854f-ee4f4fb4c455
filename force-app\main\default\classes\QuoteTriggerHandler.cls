public with sharing class Quote<PERSON><PERSON>ger<PERSON>and<PERSON> extends <PERSON>gger<PERSON>andler {
    public QuoteTriggerHandler() {
        super('Quote');
    }
   
    // public override void doBeforeUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
    //     List<Quote> newQuotes = (List<Quote>)newList;
    //     Map<Id, Quote> oldQuotes = (Map<Id, Quote>)oldMap;
    //     // 检查报价状态，防止在特定状态下编辑
    //     validateQuoteEditPermission(newQuotes, oldQuotes);
    // }

    public override void doAfterUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        List<Quote> newQuotes = (List<Quote>)newList;
        Map<Id, Quote> oldQuotes = (Map<Id, Quote>)oldMap;
        handleinstallitem(newQuotes,oldQuotes);
        // 处理审批通过的报价单，更新销售状态并同步到SAP
        handleApprovedQuotes(newQuotes, oldQuotes);
    }
    
    // ================== override方法结束，自定义方法开始 ==================


    private void handleinstallitem(List<Quote> newQuotes, Map<Id, Quote> oldQuotes){
        List<Installments_Item__c> installmentsToInsert = new List<Installments_Item__c>();//分期期数
        for(Quote newQuote : newQuotes) {
            Quote oldQuote = oldQuotes.get(newQuote.Id);
            if(newQuote.Number_of_Installments__c != oldQuote.Number_of_Installments__c) {
                System.debug('Number_of_Installments__c is changed from ' + oldQuote.Number_of_Installments__c + ' to ' + newQuote.Number_of_Installments__c);
                // 新增：根据期数字段生成Installment__c对象
                if (newQuote.Number_of_Installments__c != null && newQuote.Number_of_Installments__c > 0) {
                    for (Integer i = 1; i <= Integer.valueOf(newQuote.Number_of_Installments__c); i++) {
                        Installments_Item__c inst = new Installments_Item__c();
                        inst.Quote__c = newQuote.Id;
                        inst.InstallNo__c = i;
                        installmentsToInsert.add(inst);
                    }
                }
            }
        }
        //新增：分期期数
        System.debug(installmentsToInsert.size());
        System.debug(installmentsToInsert);
     
        if (!installmentsToInsert.isEmpty()) {
            insert installmentsToInsert;
        }
    }
    /**
     * 处理审批通过的报价单
     * 1. 对于有销售状态记录的客户，将销售状态更新为转商中
     * 2. 根据客户SAP_Num__c字段是否为空，同步客户信息到SAP
     */
    private void handleApprovedQuotes(List<Quote> newQuotes, Map<Id, Quote> oldQuotes) {
        Set<Id> accountIdsToProcess = new Set<Id>();
        Map<Id, Quote> approvedQuotes = new Map<Id, Quote>();
        
        // 筛选出状态变为已审批的报价单
        for(Quote newQuote : newQuotes) {
            Quote oldQuote = oldQuotes.get(newQuote.Id);
            System.debug('检查Quote状态变化 - QuoteId: ' + newQuote.Id + ', 旧状态: ' + oldQuote.Status + ', 新状态: ' + newQuote.Status);

            if(newQuote.Status != oldQuote.Status && newQuote.Status == 'Approved') {
                System.debug('Quote状态变为已审批，添加到处理列表 - QuoteId: ' + newQuote.Id + ', AccountId: ' + newQuote.AccountId);
                accountIdsToProcess.add(newQuote.AccountId);
                approvedQuotes.put(newQuote.Id, newQuote);

            }

            System.debug('调用Quote同步到SAP接口 - QuoteId: ' + newQuote.Id);
            try {
                Interface_CRMSyncQuoteToSAP.doSyncQuoteToSAP(newQuote.Id);
                System.debug('Quote同步到SAP接口调用成功 - QuoteId: ' + newQuote.Id);
            } catch(Exception e) {
                System.debug('Quote同步到SAP接口调用失败 - QuoteId: ' + newQuote.Id + ', 错误: ' + e.getMessage());
                System.debug('错误堆栈: ' + e.getStackTraceString());
            }
        }
        
        System.debug('accountIdsToProcess => ' + accountIdsToProcess.size());
        if(accountIdsToProcess.isEmpty()) {
            return;
        }
        // 查询相关客户
        Map<Id, Account> accountMap = new Map<Id, Account>([
            SELECT Id, SAP_Num__c, 
            (SELECT Id, Product_Line__c, Sales_Status__c FROM ChildAccounts__r)
            FROM Account 
            WHERE Id IN :accountIdsToProcess
        ]);
        // 批量处理销售状态记录更新和SAP同步
        updateSalesStatusAndSyncToSAP(approvedQuotes, accountMap);
        

    }
    
    /**
     * 更新销售状态记录并同步到SAP
     */
    private void updateSalesStatusAndSyncToSAP(Map<Id, Quote> approvedQuotes, Map<Id, Account> accountMap) {
        System.debug('=== updateSalesStatusAndSyncToSAP 方法开始 ===');
        System.debug('输入参数 - approvedQuotes数量: ' + approvedQuotes.size());
        System.debug('输入参数 - accountMap数量: ' + accountMap.size());
        System.debug('输入参数 - approvedQuotes详情: ' + approvedQuotes);
        System.debug('输入参数 - accountMap详情: ' + accountMap);

        List<Sales_Status__c> salesStatusToUpdate = new List<Sales_Status__c>();
        Map<Id, String> createAccountIdMap = new Map<Id, String>();
        // Map<Id, String> updateAccountIdMap = new Map<Id, String>();
        List<Sales_Status__c> salesStatusToInsert = new List<Sales_Status__c>();

        for(Quote quote : approvedQuotes.values()) {
            System.debug('--- 处理Quote: ' + quote.Id + ', AccountId: ' + quote.AccountId + ', Product_Cate__c: ' + quote.Product_Cate__c);

            Account account = accountMap.get(quote.AccountId);
            String productLine = quote.Product_Cate__c;
            Boolean needSync = false;

            System.debug('获取到的Account: ' + account);
            if(account != null) {
                System.debug('Account.SAP_Num__c: ' + account.SAP_Num__c);
                System.debug('Account.ChildAccounts__r: ' + account.ChildAccounts__r);
                System.debug('Account.ChildAccounts__r数量: ' + (account.ChildAccounts__r != null ? account.ChildAccounts__r.size() : 0));
            }

            // 检查账户是否有销售状态记录
            if(account != null && account.ChildAccounts__r != null && account.ChildAccounts__r.size() > 0) {
                System.debug('开始检查销售状态记录，产品线: ' + productLine);

                // 更新对应产品线的销售状态为"转商中"
                for(Sales_Status__c salesStatus : account.ChildAccounts__r) {
                    System.debug('检查销售状态记录 - ID: ' + salesStatus.Id + ', Product_Line__c: ' + salesStatus.Product_Line__c + ', Sales_Status__c: ' + salesStatus.Sales_Status__c);

                    if(salesStatus.Product_Line__c == productLine && salesStatus.Sales_Status__c != '03') {
                        System.debug('匹配到需要更新的销售状态记录，将状态从 ' + salesStatus.Sales_Status__c + ' 更新为 02');
                        salesStatus.Sales_Status__c = '02';
                        salesStatusToUpdate.add(salesStatus);
                        needSync = true;
                    } else {
                        System.debug('销售状态记录不匹配 - 产品线匹配: ' + (salesStatus.Product_Line__c == productLine) + ', 状态不为03: ' + (salesStatus.Sales_Status__c != '03'));
                    }
                }
            } else {
                System.debug('账户没有销售状态记录或账户为空');
            }

            System.debug('needSync标志: ' + needSync);

            // 根据SAP_Num__c判断同步类型
            if(account != null && needSync) {
                System.debug('满足同步条件，添加到createAccountIdMap');
                // if(String.isEmpty(account.SAP_Num__c)) {
                    createAccountIdMap.put(account.Id, productLine);
                    System.debug('已添加到createAccountIdMap - AccountId: ' + account.Id + ', ProductLine: ' + productLine);
                // } else {
                //     updateAccountIdMap.put(account.Id, productLine);
                // }
            } else {
                System.debug('不满足同步条件 - account不为空: ' + (account != null) + ', needSync: ' + needSync);
            }
        }

        System.debug('处理完成 - salesStatusToUpdate数量: ' + salesStatusToUpdate.size());
        System.debug('处理完成 - createAccountIdMap数量: ' + createAccountIdMap.size());
        System.debug('salesStatusToUpdate详情: ' + salesStatusToUpdate);
        System.debug('createAccountIdMap详情: ' + createAccountIdMap);

        // 批量更新销售状态记录
        if(!salesStatusToUpdate.isEmpty()) {
            System.debug('开始更新销售状态记录，数量: ' + salesStatusToUpdate.size());
            try {
                update salesStatusToUpdate;
                System.debug('销售状态记录更新成功');
            } catch(Exception e) {
                System.debug('销售状态记录更新失败: ' + e.getMessage());
                System.debug('错误堆栈: ' + e.getStackTraceString());
            }
        } else {
            System.debug('没有需要更新的销售状态记录');
        }

        // 批量插入销售状态记录
        if(!salesStatusToInsert.isEmpty()) {
            System.debug('开始插入销售状态记录，数量: ' + salesStatusToInsert.size());
            try {
                insert salesStatusToInsert;
                System.debug('销售状态记录插入成功');
            } catch(Exception e) {
                System.debug('销售状态记录插入失败: ' + e.getMessage());
                System.debug('错误堆栈: ' + e.getStackTraceString());
            }
        } else {
            System.debug('没有需要插入的销售状态记录');
        }

        // 批量同步到SAP
        if(createAccountIdMap.keySet().size() > 0) {
            System.debug('开始同步到SAP，账户数量: ' + createAccountIdMap.keySet().size());
            System.debug('同步到SAP的账户详情: ' + createAccountIdMap);
            try {
                Interface_CRMSyncAccountToSAP.doSyncAccountToSAP(createAccountIdMap);
                System.debug('SAP同步调用成功');
            } catch(Exception e) {
                System.debug('SAP同步调用失败: ' + e.getMessage());
                System.debug('错误堆栈: ' + e.getStackTraceString());
            }
        } else {
            System.debug('没有需要同步到SAP的账户');
        }

        // if(updateAccountIdMap.keySet().size() > 0) {
        //     Interface_CRMSyncAccountToSAP.doSyncAccountToSAP(updateAccountIdMap, 'update');
        // }

        System.debug('=== updateSalesStatusAndSyncToSAP 方法结束 ===');
    }

    /**
     * 验证报价编辑权限
     * 如果状态为提交审批或者审批通过了，就不能去编辑详情页上面的数据
     * 如果状态为草稿或拒绝可以去编辑详情页上面的数据
     * 新增：在提交审批或审批通过状态下，只允许编辑Status__c字段，其他字段不能编辑
     */
    // private void validateQuoteEditPermission(List<Quote> newQuotes, Map<Id, Quote> oldQuotes) {
    //     for (Quote newQuote : newQuotes) {
    //         Quote oldQuote = oldQuotes.get(newQuote.Id);

    //         // 如果状态是提交审批或审批通过
    //         if (newQuote.ApprovalStatus__c == 'Waiting fror Approval' || newQuote.ApprovalStatus__c == 'Approved') {
    //             // 检查是否有除Status__c字段外的其他字段被修改
    //             if (hasOtherFieldsChanged(newQuote, oldQuote)) {
    //                 newQuote.addError('当前'+newQuote.ApprovalStatus__c+'状态下只允许修改状态字段，不允许修改其他报价信息');
    //             }
    //         }
    //     }
    // }

    /**
     * 检查除Status__c字段外的其他字段是否发生变化
     */
    // private Boolean hasOtherFieldsChanged(Quote newQuote, Quote oldQuote) {
    //     // 检查关键业务字段是否发生变化，排除Status__c字段
    //     return (newQuote.Name != oldQuote.Name ||
    //             newQuote.AccountId != oldQuote.AccountId ||
    //             newQuote.ContactId != oldQuote.ContactId ||
    //             newQuote.OpportunityId != oldQuote.OpportunityId ||
    //             newQuote.ExpirationDate != oldQuote.ExpirationDate ||
    //             newQuote.Description != oldQuote.Description ||
    //             newQuote.Special_Matters__c != oldQuote.Special_Matters__c ||
                
    //             newQuote.LineItemCount != oldQuote.LineItemCount ||
    //             newQuote.GrandTotal != oldQuote.GrandTotal ||
    //             newQuote.Discount != oldQuote.Discount ||
    //             newQuote.Tax != oldQuote.Tax ||
    //             newQuote.ApprovalStatus__c != oldQuote.ApprovalStatus__c ||
    //             newQuote.ExchangeRateIdentification__c != oldQuote.ExchangeRateIdentification__c ||
    //             newQuote.Non_Standard_Rate__c != oldQuote.Non_Standard_Rate__c ||
    //             newQuote.Potential_Cost_Reminder__c != oldQuote.Potential_Cost_Reminder__c ||
    //             newQuote.sof_Descript__c != oldQuote.sof_Descript__c ||
    //             newQuote.Add_Type__c != oldQuote.Add_Type__c ||
    //             newQuote.previous_contracts__c != oldQuote.previous_contracts__c ||
    //             // 添加其他重要的业务字段
    //             newQuote.Product_Cate__c != oldQuote.Product_Cate__c ||
    //             newQuote.Payment_Way__c != oldQuote.Payment_Way__c ||
    //             newQuote.Payment_Cycle__c != oldQuote.Payment_Cycle__c ||
    //             newQuote.StartDate__c != oldQuote.StartDate__c ||
    //             newQuote.EndDate__c != oldQuote.EndDate__c ||
    //             newQuote.Number_of_Installments__c != oldQuote.Number_of_Installments__c ||
    //             newQuote.DepositAmount__c != oldQuote.DepositAmount__c ||
    //             newQuote.Is_Deposit__c != oldQuote.Is_Deposit__c ||
    //             newQuote.Contract_Cur__c != oldQuote.Contract_Cur__c ||
    //             newQuote.Settlement_Cur__c != oldQuote.Settlement_Cur__c ||
    //             newQuote.TaxCode__c != oldQuote.TaxCode__c ||
    //             newQuote.PartyB_Signing_Company__c != oldQuote.PartyB_Signing_Company__c);
    // }

}