@RestResource(urlMapping='/ProductSyncRestService/*')
global with sharing class ProductSyncRestService {
    @HttpPost
    global static String processProductRequest() {
        RestResponses res = new RestResponses();
        try {
            // 获取请求数据
            RestRequest req = RestContext.request;
            String body = req.requestBody.toString();
            Map<String, Object> productinfoMap = (Map<String, Object>)JSON.deserializeUntyped(body);
            
            // 创建产品对象并填充字段（关键修复：字段名加引号）
            Product2 product = new Product2();
            product.Material_Type__c = (String)productinfoMap.get('MTART');       // 加单引号
            product.ProductCode = (String)productinfoMap.get('MATNR');            // 加单引号
            product.Description = (String)productinfoMap.get('MAKTX');            // 加单引号
            //product.Material_Description_English__c = (String)productinfoMap.get('MAKTX'); // 加单引号
            product.Family = (String)productinfoMap.get('SPART');                 // 加单引号
            
            insert product;
            
            // 成功响应
            res.MSGTY = 'S';
            res.MSGTX = '产品创建成功，ID: ' + product.Id;
            return JSON.serialize(res);
        } catch (Exception e) {
            // 错误处理
            res.MSGTY = 'E';
            res.MSGTX = '错误: ' + e.getMessage() + 
                         '\n堆栈: ' + e.getStackTraceString();
            return JSON.serialize(res);
        }
    }

    // 响应结构（添加构造函数初始化默认值）
    global class RestResponses {
        global String MSGTY = ''; // 消息类型
        global String MSGTX = ''; // 消息文本
        global String MSGID = ''; // 消息ID
        global String MSGNO = ''; // 消息编号
        global String UUID = '';  // 唯一标识
        
        // 可选：添加构造函数
        public RestResponses() {
            this.UUID = generateUUID();
        }
        
        private String generateUUID() {
            Blob b = Crypto.generateAesKey(128);
            return EncodingUtil.convertToHex(b);
        }
    }
}