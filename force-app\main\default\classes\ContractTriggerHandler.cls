/**
 * Author: Dean
 * Date: 2025-07-09
 * Description: 合同触发器处理类
 * 1. 在合同插入或更新时，若法务或产品经理填写了特殊备注，则自动将“特殊备注已填写”字段设为true，否则为false
 * 2. 合同审批通过后生成合同唯一编号
 * 3. 合同审批通过后生成合同追溯码
 * 4. 合同审批通过后，发起queue创建收款计划
 * 
 * Test Class: ContractTriggerHandlerTest
 * Change Log:
 * 2025-07-09: Created
 */
public without sharing class ContractTriggerHandler extends TriggerHandler {
    Map<String, String> typeConvertMap = new Map<String, String>{
        'P1' => 'Z001',
        'P2' => 'Z002',
        'P3' => 'Z003',
        'P4' => 'Z004',
        'P5' => 'Z005'
    };
    Map<String,String> statusConvertMap =  new Map<String,String>{
        'Draft' => 'Draft',
        'Approval_Pending' => 'In Approval Process',
        'Approved' => 'Activated',
        'Rejected' => 'Draft',
        'Sent_to_Client' => 'Activated',
        '​Contract_Signed' => 'Activated',
        'Contract_Not_Signed' => 'Draft'
    };
    
    public static String CONTRACTRENEW_RECORDTYPEID;
    public static String CONTRACTAMENDMENT_RECORDTYPEID;
    public static String CONTRACTNEW_RECORDTYPEID;
    

    public ContractTriggerHandler() {
        super('Contract');
        if(CONTRACTRENEW_RECORDTYPEID == null)CONTRACTRENEW_RECORDTYPEID = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractRenew').getRecordTypeId();
        if(CONTRACTAMENDMENT_RECORDTYPEID == null)CONTRACTAMENDMENT_RECORDTYPEID = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractAmendment').getRecordTypeId();
        if(CONTRACTNEW_RECORDTYPEID == null) CONTRACTNEW_RECORDTYPEID = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('NewContract').getRecordTypeId();
    }

    public override void doBeforeInsert(List<SObject> newList) {
        List<Contract> newContracts = (List<Contract>)newList;
        SetAutoFullfilledFields(newContracts);
        SetContractRegion(newContracts);
        SetContractApprover(newContracts);
    }

    public override void doBeforeUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        List<Contract> newContracts = (List<Contract>)newList;
        Map<Id, Contract> oldContracts = (Map<Id, Contract>)oldMap;
        SetAutoFullfilledFields(newContracts);
        SetContractCode(newContracts,oldContracts);
        SetContractTraceabilityCode(newContracts);
    }

    public override void doAfterUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        List<Contract> newContracts = (List<Contract>)newList;
        Map<Id, Contract> oldContracts = (Map<Id, Contract>)oldMap;
        Set<Id> contractIds = new Set<Id>();
        for(Contract newCon:newContracts){
            Contract oldCon = oldContracts.get(newCon.Id);
            if(newCon.Contract_Stage__c != oldCon.Contract_Stage__c && newCon.Contract_Stage__c == 'Approved'){
                contractIds.add(newCon.Id);
            }
        }
        if(contractIds.size() > 0){
            System.enqueueJob(new CreatePaymentPlanQueue(contractIds));
        }
    }

    //自动带出值的赋值逻辑
    public void SetAutoFullfilledFields(List<Contract> contracts){
        Map<id,Account> accountMap = new Map<id,Account>([SELECT Id,SAP_Num__c from Account Where Customer_Category__c = 'C10']);

        for (Contract contract : contracts) {
            //审批的时候，若法务或产品经理填写了特殊备注，则特殊备注字段为true
            if (contract.Special_Notes__c != null && contract.Special_Notes__c != '') {
                contract.SpecialNotesFilled__c = true;
            }else{
                contract.SpecialNotesFilled__c = false;
            }
            //合同类型
            if( contract.Product_Category__c != null && typeConvertMap.containsKey(contract.Product_Category__c) ){
                contract.ContractType__c = typeConvertMap.get(contract.Product_Category__c);
            }
            //合同标准status字段
            if(statusConvertMap.containsKey(contract.Contract_Stage__c) && contract.Status != 'Activated'){
                contract.Status = statusConvertMap.get(contract.Contract_Stage__c);
            }
            //合同自定义状态字段
            if(contract.Contract_Stage__c == 'Draft') contract.Contract_Status__c = 'Draft';
            if(contract.Contract_Stage__c == 'Approval Pending')contract.Contract_Status__c = 'In Approval Process';
            //合同销售组织
            if(accountMap.containsKey(contract.PartyB_Signing_Company__c)) contract.SalesOrganization__c = accountMap.get(contract.PartyB_Signing_Company__c).SAP_Num__c;
        }
    }

    //合同审批通过后生成合同唯一编号
    public void SetContractCode(List<Contract> newContracts,Map<Id,Contract> oldContractsMap){
        //验证
        map<String,ContractConfig__c> contractAutoNoMap = new map<String,ContractConfig__c>();
        for(ContractConfig__c conCode : [SELECT id,ExternalId__c,AutoNumber__c FROM ContractConfig__c WHERE DataType__c ='Config']){
            contractAutoNoMap.put(conCode.ExternalId__c, conCode);
        }

        for(Contract c:newContracts){
            Contract oldCon = oldContractsMap.get(c.id);
            if(c.Service_Start__c == null || c.Region__c== null || c.ContractAutoNo__c != null || (oldCon.Contract_Stage__c != c.Contract_Stage__c && c.Contract_Stage__c != 'Approved')) continue;
            String key = 'SR' + String.valueOf(c.Service_Start__c.year())+c.Region__c; 
            if(contractAutoNoMap.containsKey(key)){
                ContractConfig__c currentConfig = contractAutoNoMap.get(key);
                currentConfig.AutoNumber__c = currentConfig.AutoNumber__c + 1 ;
                contractAutoNoMap.put(key,currentConfig);
                c.ContractAutoNo__c = 'SR' + String.valueOf(c.Service_Start__c.year()) + String.valueOf(currentConfig.AutoNumber__c).leftPad(3, '0') + c.Region__c;
            }
        }

        if(!contractAutoNoMap.isEmpty()) update contractAutoNoMap.values();
    }

    //合同审批通过后生成合同追溯码
    public void SetContractTraceabilityCode(List<Contract> newContracts){
        Set<id> oldContractIds = new Set<id>();
        for(Contract con:newContracts){
            if(con.OriginContract__c != null 
            && ( con.recordtypeid == CONTRACTRENEW_RECORDTYPEID || con.recordtypeid == CONTRACTAMENDMENT_RECORDTYPEID)){
                oldContractIds.add(con.OriginContract__c);
            }else if (con.OriginContract__c == null && con.recordtypeid == CONTRACTNEW_RECORDTYPEID ){
                con.ContractTraceabilityCode__c = con.ContractAutoNo__c + '-' + con.AcCompany_Code__c + '-'+ con.SalesOrganization__c;
            }
        }

        //基于老合同的变更的合同的流程
        if(oldContractIds.isEmpty()) return;
        Map<Id, Contract> oldContractMap = new Map<Id, Contract>([SELECT Id,recordtypeid,AcCompany_Code__c,ContractTypeFormula__c,SalesOrganization__c,ContractTraceabilityCode__c FROM Contract WHERE Id IN :oldContractIds]);
        system.debug('oldContractMap'+oldContractMap.values());
        for(Contract con:newContracts){
            if(con.OriginContract__c == null || !oldContractMap.containsKey(con.OriginContract__c))continue;
            Contract oldCon = oldContractMap.get(con.OriginContract__c);
            system.debug('oldCon'+oldCon);
            //拼接该合同的追溯码
            if(con.OriginContract__c != null && ( con.recordtypeid == CONTRACTRENEW_RECORDTYPEID || con.recordtypeid == CONTRACTAMENDMENT_RECORDTYPEID)){
                Boolean sameWithLastContract = true ;
                String appendCode = con.ContractTypeFormula__c ;
                if(oldcon.ContractTypeFormula__c != con.ContractTypeFormula__c && con.ContractTypeFormula__c != null){
                    sameWithLastContract = false;
                }
                if(oldCon.AcCompany_Code__c != con.AcCompany_Code__c && con.AcCompany_Code__c != null){
                    appendCode = appendCode + '-' +con.AcCompany_Code__c;
                    sameWithLastContract = false;
                }
                if(oldcon.SalesOrganization__c != con.SalesOrganization__c && con.SalesOrganization__c != null){
                    appendCode = appendCode + '-' +con.SalesOrganization__c;
                    sameWithLastContract = false;
                }

                if(oldCon.recordtypeid == CONTRACTNEW_RECORDTYPEID || !sameWithLastContract){
                    appendCode += '-01';
                    con.ContractTraceabilityCode__c = oldCon.ContractTraceabilityCode__c + '&' + appendCode;
                }else if(sameWithLastContract){
                    con.ContractTraceabilityCode__c = setVersionPlusTraceabilityCode(oldCon.ContractTraceabilityCode__c);
                }
            }
        }
    }

    //设置合同审批人
    public void SetContractApprover(List<Contract> contracts){
        List<User> approverList = [SELECT Id,UserRole.Name,UserRole.DeveloperName FROM User WHERE isactive = true ]; 
        map<String,List<User>> approverMap = new map<String,List<User>>();
        for(User u:approverList){
            if(!approverMap.containsKey(u.UserRole.DeveloperName)){
                approverMap.put(u.UserRole.DeveloperName,new List<User>());
            }
            approverMap.get(u.UserRole.DeveloperName).add(u);
        }

        for(Contract con:contracts){
            //对应产品经理
            if(con.Product_Category__c == 'P1' && approverMap.containsKey('MSPManager'))con.ProductManagerApprover__c = approverMap.get('MSPManager')[0].id;
            if(con.Product_Category__c == 'P2' && approverMap.containsKey('MASSManager'))con.ProductManagerApprover__c = approverMap.get('MASSManager')[0].id;
            if(con.Product_Category__c == 'P3' && approverMap.containsKey('CulProductManager'))con.ProductManagerApprover__c = approverMap.get('CulProductManager')[0].id;
            if(con.Product_Category__c == 'P4' && approverMap.containsKey('Others'))con.ProductManagerApprover__c = approverMap.get('Others')[0].id;
            if(con.Product_Category__c == 'P5' && approverMap.containsKey('AI'))con.ProductManagerApprover__c = approverMap.get('AI')[0].id;
            if(con.Product_Category__c == 'P6' && approverMap.containsKey('Others'))con.ProductManagerApprover__c = approverMap.get('Others')[0].id;
            //区域经理
            if(con.Region__c == 'HD' && approverMap.containsKey('EastSalesManager'))con.RSMApprover__c = approverMap.get('EastSalesManager')[0].id;
            if(con.Region__c == 'HB' && approverMap.containsKey('NothSalesManager'))con.RSMApprover__c = approverMap.get('NothSalesManager')[0].id;
            if(con.Region__c == 'HN' && approverMap.containsKey('SouthSalesManager'))con.RSMApprover__c = approverMap.get('SouthSalesManager')[0].id;
            if(con.Region__c == 'HX' && approverMap.containsKey('WestSalesManager'))con.RSMApprover__c = approverMap.get('WestSalesManager')[0].id;
            if(con.Region__c == 'HW' && approverMap.containsKey('OverseasSalesManager'))con.RSMApprover__c = approverMap.get('OverseasSalesManager')[0].id;
            //产品总监
            if(approverMap.containsKey('ProductDepManager'))con.ProductDirectorApprover__c = approverMap.get('ProductDepManager')[0].id;
            //财务部经理
            if(approverMap.containsKey('FinanceDepartment'))con.FinanceManagerApprover__c = approverMap.get('FinanceDepartment')[0].id;
            //营销总监
            if(approverMap.containsKey('MarketingDep'))con.MarketingDirectorApprover__c = approverMap.get('MarketingDep')[0].id;
            //CEO
            if(approverMap.containsKey('CEO'))con.CEOApproval__c = approverMap.get('CEO')[0].id;
            //税务经理
            //客服经理
            if(approverMap.containsKey('Service'))con.AccountManagerApprover__c = approverMap.get('Service')[0].id;
        }
    }

    //合同创建后根据Owner带出所在区域
    public void SetContractRegion(List<Contract> contracts){
        map<id,id> userMap = new map<id,id>();
        map<id,String> regionCodeMap = new Map<id,String>();
        for(Contract con:contracts){
            if(con.Region__c == null)userMap.put(con.id,con.ownerid);
        }
        for(User u:[Select id,SalesArea__c FROM user where id IN:userMap.values()]){
            if(u.SalesArea__c != null)regionCodeMap.put(u.id,u.SalesArea__c);
        }
        for(Contract conInsert:contracts){
            if(conInsert.Region__c == null && regionCodeMap.containsKey(conInsert.ownerid) ){
                if(regionCodeMap.containsKey(conInsert.ownerid)){
                    conInsert.Region__c = regionCodeMap.get(conInsert.ownerid);
                }else{
                    conInsert.Region__c.addError('无法根据合同所有人判断他所在的区域，请检查用户上的销售区域字段是否正确');
                }
            }
        }            
    }

    //解析编码的最后一个版本号并加一
    public String setVersionPlusTraceabilityCode(String text ){
        List<String> parts = text.split('&');
        String lastPart = parts[parts.size() - 1];
        List<String> subParts = lastPart.split('-');
        String lastNumber = subParts[subParts.size() - 1];
        Integer lastDashIndex = text.lastIndexOf('-');
        String newTraceabilityCode;
        if (lastDashIndex != -1) newTraceabilityCode = text.substring(0, lastDashIndex + 1) + String.valueOf(Integer.valueOf(lastNumber) + 1).leftPad(2, '0');
        return newTraceabilityCode;
    }
}