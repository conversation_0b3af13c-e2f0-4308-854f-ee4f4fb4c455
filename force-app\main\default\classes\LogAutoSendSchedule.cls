global class LogAutoSendSchedule implements Schedulable {
    
 
    global final String logId;
    global final String messageGroupNumber;
    global final String type;

    global final Integer reRunBatch;
    global  String reRunNumber;
    global  BatchIF_Log__c batchData;

    global LogAutoSendSchedule() {
        this.logId = null;
        this.messageGroupNumber = null;
        this.type = null;
    }
    
    global LogAutoSendSchedule( Integer reRunBatch,String reRunNumber) {
        this.logId = null;
        this.messageGroupNumber = null;
        this.type = null;
        this.reRunBatch = reRunBatch;
        this.reRunNumber = reRunNumber;
    }
   
    global LogAutoSendSchedule(String logId) {
        this.logId = logId;
    }
    global LogAutoSendSchedule(String messageGroupNumber,String type) {
        this.messageGroupNumber = messageGroupNumber;
        this.type = type;
    }
    
    global void execute(SchedulableContext SC) {
        System.debug('+++++logId+++++' + logId + '+++++messageGroupNumber+++++' + messageGroupNumber + '+++++type+++++' + type);

       
        if(reRunBatch == 1){
           
            // NFM131Controller nfm131 = new NFM131Controller(reRunNumber);
            // nfm131.execute(batchData,null);
            // System.debug('NFM131ReRun');
            return;
        }
       

        if(String.isNotEmpty(logId)){
            Id execBTId = Database.executeBatch(new LogAutoSendBatch(logId), 1);
        }
       
        else if (String.isNotEmpty(messageGroupNumber) && String.isNotEmpty(type)) {
            Id execBTId = Database.executeBatch(new LogAutoSendBatch(type , messageGroupNumber), 1);
        }
       
        else{

            Id execBTId = Database.executeBatch(new LogAutoSendBatch(), 1);
        }
        
    }

    
    public static void assignOneMinute() {
       
        Datetime addOneM = System.now().addMinutes(2);
        String CRON_EXP = '0 ' + addOneM.minute() + ' ' + addOneM.hour() + ' ' + addOneM.day() + ' ' + addOneM.month() + ' ? ' + addOneM.year();
        List<CronTrigger> oldcron = [select Id from CronTrigger where CronExpression = :CRON_EXP and CronJobDetail.Name like 'LogAutoSend%'];
        if (oldcron.size() == 0) {
            System.schedule('LogAutoSend' + CRON_EXP, CRON_EXP, new LogAutoSendSchedule());
        }
        for (CronTrigger ct :
                [SELECT Id FROM CronTrigger WHERE State = 'DELETED' and CronJobDetail.Name like 'LogAutoSend%']) {
            System.abortJob(ct.id);
        }
    }
    /**
     * @fuction   [发接口错误的时重发只发送自己这一条，通过messageGroupNumber和type确认唯一一条]
     * <AUTHOR> @DateTime  
     */
    public static void assignOneMinute(String messageGroupNumber,String type) {
      
        Datetime addOneM = System.now().addMinutes(2);
        String CRON_EXP = '0 ' + addOneM.minute() + ' ' + addOneM.hour() + ' ' + addOneM.day() + ' ' + addOneM.month() + ' ? ' + addOneM.year();
        List<CronTrigger> oldcron = [select Id from CronTrigger where CronExpression = :CRON_EXP and CronJobDetail.Name like 'LogAutoSend%'];
        // if (oldcron.size() == 0) {
            Datetime nowTime = Datetime.now();
            // System.schedule(+'LogAutoSend'+ CRON_EXP, CRON_EXP, new LogAutoSendSchedule( messageGroupNumber, type));
            System.schedule(+'LogAutoSend'+nowTime+'And'+messageGroupNumber + CRON_EXP, CRON_EXP, new LogAutoSendSchedule( messageGroupNumber, type));
        
        // }
        for (CronTrigger ct :
                [SELECT Id FROM CronTrigger WHERE State = 'DELETED' and CronJobDetail.Name like 'LogAutoSend%']) {
            System.abortJob(ct.id);
        }
    }
}