global with sharing class Interface_SAPSyncProductPriceToSFDC extends Interface_InboundBase{
    public Map<String, Product2> code_productMap;
    global override Map<String, Object> execute(Interface_InboundParam param){

        Map<String, Object> resultMap = new Map<String, Object>();
        code_productMap = new Map<String, Product2>();
        // 拿到数据后的具体操作从这里进行
        List<ProductPrice__c> productPriceInsertList = new List<ProductPrice__c>();
        List<Price_Grade__c> priceGradeInsertList = new List<Price_Grade__c>();
        List<Interface_ProductPriceObject.PriceItem> priceItemList = new List<Interface_ProductPriceObject.PriceItem>();
        try{
            
            Interface_ProductPriceObject priceRecord = (Interface_ProductPriceObject)JSON.deserialize(param.dataString, Interface_ProductPriceObject.class);
            resultMap.put('uuid',priceRecord.uuid);
            Set<String> productCodeSet = new Set<String>();
            
            for (Interface_ProductPriceObject.PriceItem record : priceRecord.TLIST) {
            productCodeSet.add(record.MATNR);
            }
            if (!productCodeSet.isEmpty()) {
                code_productMap = Service_Product.searchProductByProductCode(productCodeSet);
            }

            for (Interface_ProductPriceObject.PriceItem record : priceRecord.TLIST) {
                productPriceInsertList.add(generateProductPrice(record));
                for (Interface_ProductPriceObject.PriceGrade grade : record.ITEM) {
                    priceGradeInsertList.add(generatePriceGrade(record.KNUMH, grade));
                }
            }
    
            Database.upsert(productPriceInsertList, ProductPrice__c.RecordNumberSAP__c);
            Database.upsert(priceGradeInsertList);
            resultMap.put('msgty','S');
            resultMap.put('msgtx', 'Success');
                
                //send up
        } catch (Exception e) {
            
            resultMap.put('msgty','E');
            resultMap.put('msgtx', e.getMessage());
            
        } 
        
        resultMap.put('msgid', '');
        resultMap.put('msgno', '');
        resultMap.put('sapnum','');
        resultMap.put('field1','');
        resultMap.put('field2','');
        resultMap.put('field3','');
        // 设置响应头和状态码
        RestContext.response.addHeader('Content-Type', 'application/json');
        RestContext.response.statusCode = 200;
        RestContext.response.responseBody = Blob.valueOf(JSON.serialize(resultMap));
        return resultMap;
    }

    public ProductPrice__c generateProductPrice(Interface_ProductPriceObject.PriceItem item){
        ProductPrice__c price = new ProductPrice__c();
        price.RecordNumberSAP__c = item.KNUMH;
        price.Sales_Organization__c = item.VKORG;
        price.Distribution_Channel__c = item.VTWEG;
        price.Product_Area__c = item.ZAREA;
        price.Product_Code__c = item.MATNR;
        price.Amount__c = Decimal.valueOf(item.KBETR.trim());
        price.CurrencyIsoCode = item.KONWA;
        price.Price_Unit__c = Decimal.valueOf(item.KPEIN.trim());//价格单位
        price.Unit__c = item.KMEIN.trim();//计量单位
        price.Valid_From__c = Interface_ProductPriceObject.parseSAPDate(item.DATAB);
        price.Valid_To__c = Interface_ProductPriceObject.parseSAPDate(item.DATBI) ;
        System.debug('p'+code_productMap);
        System.debug('price.Product_Code__c'+price.Product_Code__c);
        if (!code_productMap.isEmpty()&&code_productMap.containsKey(price.Product_Code__c)) {
            price.Product__c = code_productMap.get(price.Product_Code__c).Id;
        }
        return price;
  }

  public Price_Grade__c generatePriceGrade(String priceNumber, Interface_ProductPriceObject.PriceGrade grade){
      Price_Grade__c priceGrade = new Price_Grade__c();
      priceGrade.ProductPrice__r = new ProductPrice__c(RecordNumberSAP__c = priceNumber);
      priceGrade.GradeCode_SAP__c = grade.KLFN1;
      priceGrade.GradeQuantity__c = Decimal.valueOf(grade.KSTBM.trim());
      priceGrade.IntervalPrice__c = Decimal.valueOf(grade.KBETR.trim());
      return priceGrade;
  }
}