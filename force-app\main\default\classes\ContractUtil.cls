global with sharing class ContractUtil {
    // 按钮调用需要静态的全局方法
    @AuraEnabled
    public static ResultWrapper generateContractCode(String contractId) {
        ResultWrapper result = new ResultWrapper();
        result.success = true;
        result.message = '合同编码已生成';

        map<String,ContractConfig__c> contractAutoNoMap = new map<String,ContractConfig__c>();
        List<ContractConfig__c> pendingContractDataList = new List<ContractConfig__c>();
        for(ContractConfig__c conCode : [SELECT id,ExternalId__c,AutoNumber__c FROM ContractConfig__c WHERE DataType__c ='Config']){
            contractAutoNoMap.put(conCode.ExternalId__c, conCode);
        }

        Contract c = [SELECT id,Service_Start__c,Region__c,ContractAutoNo__c,Contract_Stage__c FROM Contract WHERE Id =:contractId];

        if(c.ContractAutoNo__c == null || c.ContractAutoNo__c =='' || c.Service_Start__c == null || c.Region__c== null || c.ContractAutoNo__c != null ||  c.Contract_Stage__c == 'Approved') {
            result.success = false;
            result.message = '不符合生成合同编码的要求，请检查数据';
            return result;
        }
        String key = 'SR' + String.valueOf(c.Service_Start__c.year())+c.Region__c; 
        if(contractAutoNoMap.containsKey(key)){
            ContractConfig__c currentConfig = contractAutoNoMap.get(key);
            currentConfig.AutoNumber__c = currentConfig.AutoNumber__c + 1 ;
            contractAutoNoMap.put(key,currentConfig);
            c.ContractAutoNo__c = 'SR' + String.valueOf(c.Service_Start__c.year()) + String.valueOf(currentConfig.AutoNumber__c).leftPad(3, '0') + c.Region__c;
            pendingContractDataList.add(currentConfig);
        }

        if(pendingContractDataList.size() > 0){
            update pendingContractDataList;
            update c;
        } 

        
        return result;
    }

    //合同审批通过后生成合同追溯码
    @AuraEnabled
    public static ResultWrapper generateContractTraceCode(String contractId){
        ResultWrapper result = new ResultWrapper();

        List<Contract>  updateContracts = new List<Contract>();
        String CONTRACTRENEW_RECORDTYPEID = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractRenew').getRecordTypeId();
        String CONTRACTAMENDMENT_RECORDTYPEID = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractAmendment').getRecordTypeId();
        String CONTRACTNEW_RECORDTYPEID = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('NewContract').getRecordTypeId();
        Contract con = [SELECT id,recordtypeid,AcCompany_Code__c,Region__c,ContractAutoNo__c,SalesOrganization__c,ContractTraceabilityCode__c,OriginContract__c FROM Contract WHERE id=:contractId];
        Set<id> oldContractIds = new Set<id>();
        //for(Contract con:newContracts){
        if(con.ContractTraceabilityCode__c != null && con.ContractTraceabilityCode__c != '') {
            result.success = false;
            result.message = '已有合同追溯码不能更新';
            return result;
        }
        if(con.OriginContract__c != null && ( con.recordtypeid == CONTRACTRENEW_RECORDTYPEID || con.recordtypeid == CONTRACTAMENDMENT_RECORDTYPEID)){
            oldContractIds.add(con.OriginContract__c);
        }else if (con.OriginContract__c == null && con.recordtypeid == CONTRACTNEW_RECORDTYPEID ){
            con.ContractTraceabilityCode__c = con.ContractAutoNo__c + '-' + con.AcCompany_Code__c + '-'+ con.SalesOrganization__c;
            update con;

            result.success = true;
            result.message = '合同已更新追溯码';
            return result;
        }
        //}

        //基于老合同的变更的合同的流程
        if(oldContractIds.isEmpty()){
            result.success = false;
            result.message = '合同变更续签需要先关联老合同';
            return result;
        }
        Map<Id, Contract> oldContractMap = new Map<Id, Contract>([SELECT Id,recordtypeid,AcCompany_Code__c,ContractTypeFormula__c,SalesOrganization__c,ContractTraceabilityCode__c FROM Contract WHERE Id IN :oldContractIds]);
        system.debug('oldContractMap'+oldContractMap.values());
        //for(Contract con:newContracts){
            if(con.OriginContract__c == null || !oldContractMap.containsKey(con.OriginContract__c)){
                result.success = false;
                result.message = '没有找到老合同';
                return result;
            }
            Contract oldCon = oldContractMap.get(con.OriginContract__c);
            system.debug('oldCon'+oldCon);
            //拼接该合同的追溯码
            if(con.OriginContract__c != null && ( con.recordtypeid == CONTRACTRENEW_RECORDTYPEID || con.recordtypeid == CONTRACTAMENDMENT_RECORDTYPEID)){
                Boolean sameWithLastContract = true ;
                String appendCode = con.ContractTypeFormula__c ;
                if(oldcon.ContractTypeFormula__c != con.ContractTypeFormula__c && con.ContractTypeFormula__c != null){
                    sameWithLastContract = false;
                }
                if(oldCon.AcCompany_Code__c != con.AcCompany_Code__c && con.AcCompany_Code__c != null){
                    appendCode = appendCode + '-' +con.AcCompany_Code__c;
                    sameWithLastContract = false;
                }
                if(oldcon.SalesOrganization__c != con.SalesOrganization__c && con.SalesOrganization__c != null){
                    appendCode = appendCode + '-' +con.SalesOrganization__c;
                    sameWithLastContract = false;
                }

                if(oldCon.recordtypeid == CONTRACTNEW_RECORDTYPEID || !sameWithLastContract){
                    appendCode += '-01';
                    con.ContractTraceabilityCode__c = oldCon.ContractTraceabilityCode__c + '&' + appendCode;
                }else if(sameWithLastContract){
                    con.ContractTraceabilityCode__c = setVersionPlusTraceabilityCode(oldCon.ContractTraceabilityCode__c);
                }
                updateContracts.add(con);
            }
        //}

        if(!updateContracts.isEmpty()) {
            update updateContracts;
            result.success = true;
            result.message = '合同已更新追溯码';
            return result;
        }
        result.success = true;
        result.message = '无需要更新的数据';
        return result;
    }

    @AuraEnabled
    public static ResultWrapper RegenerateCollectionPlan(String contractId){
        ResultWrapper result = new ResultWrapper();
        List<CollectionPlan__c> collectionPlans = [SELECT Id FROM CollectionPlan__c WHERE Contract__c = :contractId];
        if(collectionPlans.size() > 0){
            result.success = false;
            result.message = '已存在收款计划';
            return result;
        }
        set<id> contractIds = new set<id>();
        contractIds.add(contractId);
        System.enqueueJob(new CreatePaymentPlanQueue(contractIds));
        result.success = true;
        result.message = '收款计划创建中，请在作业执行完成后查收';
        return result;
    }

    private static String setVersionPlusTraceabilityCode(String text ){
        List<String> parts = text.split('&');
        String lastPart = parts[parts.size() - 1];
        List<String> subParts = lastPart.split('-');
        String lastNumber = subParts[subParts.size() - 1];
        Integer lastDashIndex = text.lastIndexOf('-');
        String newTraceabilityCode;
        if (lastDashIndex != -1) newTraceabilityCode = text.substring(0, lastDashIndex + 1) + String.valueOf(Integer.valueOf(lastNumber) + 1).leftPad(2, '0');
        return newTraceabilityCode;
    }

    public class ResultWrapper {
        @AuraEnabled public Boolean success;
        @AuraEnabled public String message;
    }
}