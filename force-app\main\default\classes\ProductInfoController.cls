/**
 * 产品信息控制器
 * 用于获取产品列表和提供筛选功能
 */
public with sharing class ProductInfoController {
    
    /**
     * 获取产品列表
     * @param quoteId 报价单ID
     * @return 产品列表
     */
    @AuraEnabled(cacheable=true)
    public static List<Product2> getProducts(String quoteId) {
        String quoteCategory = getQuoteCategory(quoteId);
        try {
            // 查询产品
            List<Product2> products = [
                SELECT Id, Name, ProductCode, toLabel(Family), Description, QuantityUnitOfMeasure,
                       Level__c,
                       ParentProduct__r.Name, ParentProduct__r.Level__c,
                       ParentProduct__r.ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.Level__c,
                       ParentProduct__r.ParentProduct__r.ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.ParentProduct__r.Level__c
                FROM Product2
                WHERE (
                    (Level__c = '4' AND ParentProduct__r.Level__c = '3' AND ParentProduct__r.ParentProduct__r.Level__c = '2' 
                     AND ParentProduct__r.ParentProduct__r.ParentProduct__r.Level__c = '1' 
                     AND ParentProduct__r.ParentProduct__r.ParentProduct__r.Name = :quoteCategory)
                    OR
                    (Level__c = '3' AND ParentProduct__r.Level__c = '2' AND ParentProduct__r.ParentProduct__r.Level__c = '1' 
                     AND ParentProduct__r.ParentProduct__r.Name = :quoteCategory)
                )
                LIMIT 1000
            ];
            
            return products;
        } catch(Exception e) {
            System.debug('获取产品列表时发生错误: ' + e.getMessage());
            throw new AuraHandledException('获取产品列表时发生错误: ' + e.getMessage());
        }
    }
    public static String getQuoteCategory(String quoteId){
        try {
            // 查询报价单
            Quote quote = [
                SELECT Id, Name,toLabel(Product_Cate__c)
                FROM Quote
                WHERE Id = :quoteId
                LIMIT 1
            ];
            
            return quote.Product_Cate__c;
        } catch(Exception e) {
            System.debug('获取报价单名称时发生错误: ' + e.getMessage());
            throw new AuraHandledException('获取报价单名称时发生错误: ' + e.getMessage());
        }
    }
   @AuraEnabled(cacheable=true)
    public static List<Product2> getProductsFilterList(String productCode, String productName, String family) {
        
        try {
            String sql  = 'SELECT Id, Name, ProductCode, toLabel(Family) Family, Description, QuantityUnitOfMeasure, Level__c, ' +
                          'ParentProduct__r.Name, ParentProduct__r.Level__c, ' +
                          'ParentProduct__r.ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.Family,ParentProduct__r.ParentProduct__r.Level__c, ' +
                          'ParentProduct__r.ParentProduct__r.ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.ParentProduct__r.Level__c ' +
                          'FROM Product2 WHERE IsActive = false';
            if(productCode != null && productCode != '') {
                sql += ' AND ProductCode LIKE \'%' + String.escapeSingleQuotes(productCode) + '%\'';
            }
            if(productName != null && productName != '') {
                sql += ' AND Name LIKE \'%' + String.escapeSingleQuotes(productName) + '%\'';
            }
            if(family != null && family != '') {
                sql += ' AND ParentProduct__r.ParentProduct__r.Name = :family';
            }
            // 查询产品
            List<Product2> products = Database.query(sql);
            
            return products;
        } catch(Exception e) {
            System.debug('获取产品列表时发生错误: ' + e.getMessage());
            throw new AuraHandledException('获取产品列表时发生错误: ' + e.getMessage());
        }
    }

    @AuraEnabled(cacheable=true)
    public static Map<String, List<Map<String, String>>> getMaaSFilterOptions() {
        Map<String, List<Map<String, String>>> options = new Map<String, List<Map<String, String>>>{
            'levelOne' => new List<Map<String, String>>(),
            'levelTwo' => new List<Map<String, String>>(),
            'levelThree' => new List<Map<String, String>>()
        };

        Set<String> levelOneNames = new Set<String>();
        Set<String> levelTwoNames = new Set<String>();
        Set<String> levelThreeNames = new Set<String>();

        for(Product2 p : [SELECT Name, Level__c 
                            FROM Product2 
                            WHERE
                            (
                                (Level__c = '1' AND Name = 'MAAS') OR
                                (Level__c = '2' AND ParentProduct__r.Name = 'MAAS') OR
                                (Level__c = '3' AND ParentProduct__r.ParentProduct__r.Name = 'MAAS')
                            )]) {
            if(p.Level__c == '1') {
                levelOneNames.add(p.Name);
            } else if (p.Level__c == '2') {
                levelTwoNames.add(p.Name);
            } else if (p.Level__c == '3') {
                levelThreeNames.add(p.Name);
            }
        }

        for (String name : levelOneNames) {
            options.get('levelOne').add(new Map<String, String>{'label' => name, 'value' => name});
        }
        for (String name : levelTwoNames) {
            options.get('levelTwo').add(new Map<String, String>{'label' => name, 'value' => name});
        }
        for (String name : levelThreeNames) {
            options.get('levelThree').add(new Map<String, String>{'label' => name, 'value' => name});
        }
        
        return options;
    }

    @AuraEnabled(cacheable=true)
    public static List<Product2> getProductsByLevels(String levelOneName, String levelTwoName, String levelThreeName) {
        String soql = 'SELECT Id, Name, ProductCode, toLabel(Family), Description, QuantityUnitOfMeasure, Level__c, ' +
                    'ParentProduct__r.Name, ParentProduct__r.Level__c, ' +
                    'ParentProduct__r.ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.Level__c, ' +
                    'ParentProduct__r.ParentProduct__r.ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.ParentProduct__r.Level__c ' +
                    'FROM Product2 WHERE IsActive = false';
        if (String.isNotBlank(levelThreeName)) {
            soql += ' AND Name = :levelThreeName';
        }
        if (String.isNotBlank(levelTwoName)) {
            soql += ' AND ParentProduct__r.Name = :levelTwoName';
        }
        if (String.isNotBlank(levelOneName)) {
            soql += ' AND ParentProduct__r.ParentProduct__r.Name = :levelOneName';
        }

        soql += ' LIMIT 1000';
        List<Product2> levelThreeProductList = Database.query(soql);
        List<String> levelThreeProIdList = new List<String>();
        for (Product2 pro : levelThreeProductList) {
            levelThreeProIdList.add(pro.Id);
        }

        List<Product2> levelFourProductList = [SELECT Id, Name, ProductCode, toLabel(Family), Description, QuantityUnitOfMeasure, Level__c
                                                From Product2
                                                Where ParentProduct__c in:levelThreeProIdList
                                                AND IsActive = false
                                                LIMIT 1000];

        levelFourProductList.addAll(levelThreeProductList);
        return levelFourProductList;
    }

    @AuraEnabled(cacheable=true)
    public static List<Product2> getMaaSProducts() {
        List<Product2> maasProducts = new List<Product2>();

       
        maasProducts.addAll([
            SELECT Id, Name, ProductCode, toLabel(Family), Description, QuantityUnitOfMeasure, Level__c,
                   ParentProduct__r.Name, ParentProduct__r.Level__c,
                   ParentProduct__r.ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.Level__c,
                   ParentProduct__r.ParentProduct__r.ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.ParentProduct__r.Level__c
            FROM Product2
            WHERE ParentProduct__r.ParentProduct__r.Name='MaaS' or ParentProduct__r.ParentProduct__r.ParentProduct__r.Name='MaaS'
            LIMIT 1000
        ]);

        
        // maasProducts.addAll([
        //     SELECT Id, Name, ProductCode, toLabel(Family), Description, QuantityUnitOfMeasure, Level__c,
        //            ParentProduct__r.Name, ParentProduct__r.Level__c,
        //            ParentProduct__r.ParentProduct__r.Name, ParentProduct__r.ParentProduct__r.Level__c
        //     FROM Product2
        //     WHERE IsActive = false AND Level__c = '3' AND ParentProduct__r.ParentProduct__r.Name = 'MAAS'
        //     LIMIT 1000
        // ]);
        
        return maasProducts;
    }
}