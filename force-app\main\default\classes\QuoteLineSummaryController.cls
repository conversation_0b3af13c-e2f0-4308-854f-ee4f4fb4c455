public with sharing class QuoteLineSummaryController {
    public QuoteLineSummaryController() {

    }
    
    /**
     * 获取报价项及其报价方式
     * @param quoteId 报价ID
     * @return 报价项及其报价方式的包装类列表
     */
    @AuraEnabled
    public static List<ProductWrapper> getQuoteItemsWithPricingMethods(String quoteId) {
        List<ProductWrapper> result = new List<ProductWrapper>();
        
        try {
            // 查询报价项，只查询非产品组的产品 (ISGROUP=false)
            List<QuoteLineItem> quoteLineItems = [
                SELECT Id, Product2Id, Product2.Name, UnitPrice, Quantity, 
                       Tax_Rate__c, Account_ID__c, Profit_Statement__c, ISGROUP__c
                FROM QuoteLineItem
                // WHERE QuoteId = :quoteId AND Ladder_discount__c = null AND ISGROUP__c = false
                WHERE QuoteId = :quoteId AND  ISGROUP__c = false
                ORDER BY CreatedDate
            ];
            
            System.debug('查询到报价项数量: ' + quoteLineItems.size());
            
            // 获取报价方式
            Map<Id, List<Quotation_Method__c>> pricingMethodsByQLI = getPricingMethods(quoteLineItems);
            
            // 构建包装类
            for (QuoteLineItem qli : quoteLineItems) {
                ProductWrapper pw = new ProductWrapper();
                // 保留productId作为唯一标识，同时保存产品名称
                pw.productId = qli.Product2Id;
                pw.productName = qli.Product2.Name; // 添加产品名称
                pw.unitPrice = qli.UnitPrice;
                pw.taxRate = qli.Tax_Rate__c;
                pw.customerAccountId = qli.Account_ID__c;
                pw.profitDescription = qli.Profit_Statement__c;
                
                // 添加报价方式
                List<Quotation_Method__c> pricingMethods = pricingMethodsByQLI.get(qli.Id);
                if (pricingMethods != null) {
                    for (Quotation_Method__c pm : pricingMethods) {
                        QuoteTypeWrapper qtw = new QuoteTypeWrapper();
                        
                        // 根据报价方式类型设置不同字段
                        if (pm.Method__c == '保底单价*保底数量') {
                            qtw.type = 'minimumUnitPrice';
                            qtw.label = '保底单价*保底数量';
                            qtw.minimumUnitPrice = pm.Minimum_ProdUnitPrice__c;
                            qtw.minimumQuantity = pm.Minimum_Amout__c;
                        } else if (pm.Method__c == '固定用量*固定单价') {
                            qtw.type = 'fixedUsage';
                            qtw.label = '固定用量*固定单价';
                            qtw.fixedUsage = pm.Fixed_Dosage__c;
                            qtw.fixedAmount = pm.Fixed_UnitPrice__c;
                        } else if (pm.Method__c == '金额封顶') {
                            qtw.type = 'maximumAmount';
                            qtw.label = '金额封顶';
                            qtw.maximumAmount = pm.Amount_Cap__c;
                        } else if (pm.Method__c == '产品折扣') {
                            qtw.type = 'productDiscount';
                            qtw.label = '产品折扣';
                            qtw.discountCoefficient = pm.Discount_Factor__c;
                            qtw.fixedRebate = pm.Fixed_Rebate__c;
                            qtw.cashReduce = pm.Cash_Reduce__c;
                            qtw.credit = pm.Credit__c;
                        }
                        
                        pw.quoteTypes.add(qtw);
                    }
                }
                
                result.add(pw);
            }
            
            System.debug('返回包装类数量: ' + result.size());
        } catch (Exception e) {
            System.debug('获取报价项出错: ' + e.getMessage());
            throw new AuraHandledException('获取报价项出错: ' + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取报价方式
     * @param quoteLineItems 报价项列表
     * @return 按报价项ID分组的报价方式列表
     */
    private static Map<Id, List<Quotation_Method__c>> getPricingMethods(List<QuoteLineItem> quoteLineItems) {
        Map<Id, List<Quotation_Method__c>> result = new Map<Id, List<Quotation_Method__c>>();
        
        if (quoteLineItems.isEmpty()) {
            return result;
        }
        
        Set<Id> qliIds = new Set<Id>();
        for (QuoteLineItem qli : quoteLineItems) {
            qliIds.add(qli.Id);
        }
        
        // 查询报价方式
        List<Quotation_Method__c> pricingMethods = [
            SELECT Id, Quote_Line_Item_ID__c, Method__c, 
                   GuaranteedMin_Amount__c, Minimum_ProdUnitPrice__c, Minimum_Amout__c,
                   Fixed_Dosage__c, Fixed_UnitPrice__c, FixAmount__c, Amount_Cap__c,
                   Discount_Factor__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c
            FROM Quotation_Method__c
            WHERE Quote_Line_Item_ID__c IN :qliIds and ISGROUP__c = false
            ORDER BY CreatedDate
        ];
        
        System.debug('查询到报价方式数量: ' + pricingMethods.size());
        
        // 按报价项ID分组
        for (Quotation_Method__c pm : pricingMethods) {
            if (!result.containsKey(pm.Quote_Line_Item_ID__c)) {
                result.put(pm.Quote_Line_Item_ID__c, new List<Quotation_Method__c>());
            }
            result.get(pm.Quote_Line_Item_ID__c).add(pm);
        }
        
        return result;
    }
    
    /**
     * 保存报价项及其报价方式
     * @param quoteId 报价ID
     * @param productsJson 产品数据JSON字符串
     * @return 保存后的报价项及其报价方式的包装类列表
     */
    @AuraEnabled
    public static List<ProductWrapper> saveQuoteItemsWithPricingMethods(String quoteId, String productsJson) {
        try {
            System.debug( '开始保存报价项，报价ID: ' + quoteId);
            System.debug('产品数据JSON: ' + productsJson);
            
            // 解析JSON - 使用自定义解析方法而不是直接反序列化
            List<ProductWrapper> products = parseProductsJson(productsJson);
            
            // 验证每个产品的报价方式数量不超过3个
            for (Integer i = 0; i < products.size(); i++) {
                ProductWrapper product = products[i];
                if (product.quoteTypes != null && product.quoteTypes.size() > 3) {
                    throw new AuraHandledException('第' + (i + 1) + '个产品的报价方式数量超过3个，最多只能添加3个报价方式');
                }
            }
            
            try {
                // 获取现有报价项，包括ISGROUP标识
                List<QuoteLineItem> existingQLIList = [
                    SELECT Id, Product2Id, ISGROUP__c
                    FROM QuoteLineItem
                    WHERE QuoteId = :quoteId
                ];
                
                System.debug( '查询到现有报价项数量: ' + existingQLIList.size());
                
                // 构建Product2Id到QuoteLineItem的映射，只包含非产品组的产品
                Map<Id, QuoteLineItem> productIdToQLI = new Map<Id, QuoteLineItem>();
                // 记录是产品组的产品ID
                Set<Id> groupProductIds = new Set<Id>();
                for (QuoteLineItem qli : existingQLIList) {
                    if (qli.Product2Id != null) {
                        if (qli.ISGROUP__c == true) {
                            groupProductIds.add(qli.Product2Id);
                        } else {
                            productIdToQLI.put(qli.Product2Id, qli);
                        }
                    }
                }
                
                System.debug( '产品组产品ID数量: ' + groupProductIds.size());
                System.debug( '非产品组产品数量: ' + productIdToQLI.size());
                
                // 验证：不允许修改产品组的产品
                for (ProductWrapper pw : products) {
                    if (String.isNotBlank(pw.productId)) {
                        try {
                            // 
                            String productIdStr = pw.productId;
                            for (Id groupProductId : groupProductIds) {
                                if (groupProductId != null && productIdStr == groupProductId) {
                                    throw new AuraHandledException('无法修改属于产品组的产品: ' + pw.productName);
                                }
                            }
                        } catch (Exception e) {
                            System.debug( '产品ID检查失败: ' + pw.productId + ', 错误: ' + e.getMessage() + '\n' + e.getStackTraceString());
                            // 不抛出异常，继续处理
                        }
                    }
                }
                
                // 收集产品ID 
                Set<String> productIdStrings = new Set<String>();
                for (ProductWrapper pw : products) {
                    if (String.isNotBlank(pw.productId)) {
                        productIdStrings.add(pw.productId);
                    }
                }
                
                // 获取产品价格表条目 
                Map<String, String> pricebookEntryIds = new Map<String, String>();
                
                // 如果有产品ID，查询价格表条目
                if (!productIdStrings.isEmpty()) {
                    try {
                        // 查询价格表条目
                        for (PricebookEntry pbe : [
                            SELECT Id, Product2Id 
                            FROM PricebookEntry 
                            WHERE Product2Id IN :productIdStrings AND Pricebook2.IsStandard = true
                        ]) {
                            pricebookEntryIds.put(pbe.Product2Id, pbe.Id);
                        }
                        
                        System.debug( '查询到价格表条目数量: ' + pricebookEntryIds.size());
                    } catch (Exception e) {
                        System.debug('查询价格表条目出错: ' + e.getMessage() + '\n' + e.getStackTraceString());
                        
                    }
                }
                
                // 获取现有报价方式
                Map<Id, List<Quotation_Method__c>> existingPricingMethods = new Map<Id, List<Quotation_Method__c>>();
                // 收集所有非产品组的QuoteLineItem ID
                Set<Id> qliIds = new Set<Id>();
                for (QuoteLineItem qli : existingQLIList) {
                    if (qli.ISGROUP__c != true) {
                        qliIds.add(qli.Id);
                    }
                }
                
                try {
                    for (Quotation_Method__c pm : [
                        SELECT Id, Quote_Line_Item_ID__c, Method__c
                        FROM Quotation_Method__c
                        WHERE Quote_Line_Item_ID__c IN :qliIds AND ISGROUP__c = false
                    ]) {
                        if (!existingPricingMethods.containsKey(pm.Quote_Line_Item_ID__c)) {
                            existingPricingMethods.put(pm.Quote_Line_Item_ID__c, new List<Quotation_Method__c>());
                        }
                        existingPricingMethods.get(pm.Quote_Line_Item_ID__c).add(pm);
                    }
                    
                    System.debug( '查询到现有报价方式数量: ' + existingPricingMethods.size());
                } catch (Exception e) {
                    System.debug( '查询报价方式出错: ' + e.getMessage() + '\n' + e.getStackTraceString());
                    
                }
                
                // 处理报价项
                List<QuoteLineItem> qlisToUpsert = new List<QuoteLineItem>();
                Set<Id> retainedQLIIds = new Set<Id>(); // 保留的报价项ID
                Map<String, Id> productIdToNewQLIId = new Map<String, Id>(); // 新的产品ID到报价项ID的映射
                
                for (ProductWrapper pw : products) {
                    if (String.isBlank(pw.productId)) {
                        System.debug('跳过没有产品ID的数据');
                        continue; // 跳过没有产品ID的数据
                    }
                    
                    QuoteLineItem qli;
                    String productId = pw.productId;
                    
                    System.debug('处理产品: ' + pw.productName + ' (ID: ' + productId + ')');
                    
                    // 根据产品ID查找现有报价项
                    QuoteLineItem existingQLI = null;
                    
                    // 遍历现有报价项查找匹配的产品ID
                    for (QuoteLineItem existingItem : existingQLIList) {
                        if (existingItem.Product2Id == productId && existingItem.ISGROUP__c == false) {
                            existingQLI = existingItem;
                            break;
                        }
                    }
                    
                    if (existingQLI != null) {
                        // 更新现有报价项
                        qli = existingQLI;
                        retainedQLIIds.add(qli.Id);
                        System.debug( '更新现有报价项: ' + qli.Id);
                    } else {
                        // 创建新报价项
                        qli = new QuoteLineItem();
                        qli.QuoteId = quoteId;
                        qli.Product2Id = productId;
                        qli.ISGROUP__c = false; // 确保新创建的都是非产品组产品
                        System.debug('创建新报价项, 产品ID: ' + productId);
                        
                        // 设置PricebookEntryId
                        if (pricebookEntryIds.containsKey(productId)) {
                            qli.PricebookEntryId = pricebookEntryIds.get(productId);
                            System.debug('设置价格表条目ID: ' + pricebookEntryIds.get(productId));
                        } else {
                            System.debug('警告: 未找到产品的价格表条目: ' + productId);
                        }
                    }
                    
                    try {
                        // 将UnitPrice设置为前端排价输入框的值
                        qli.UnitPrice = pw.unitPrice != null ? pw.unitPrice : 0;
                        qli.Quantity = 1; 
                        qli.Tax_Rate__c = pw.taxRate != null ? pw.taxRate : 0;
                        qli.Account_ID__c = pw.customerAccountId;
                        qli.Profit_Statement__c = pw.profitDescription;
                        System.debug( '设置报价项字段 - 单价: ' + qli.UnitPrice + ', 税率: ' + qli.Tax_Rate__c);
                    } catch (Exception e) {
                        System.debug( '设置QuoteLineItem字段出错: ' + e.getMessage() + '\n' + e.getStackTraceString());
                        throw new AuraHandledException('设置产品字段出错: ' + e.getMessage());
                    }
                    
                    qlisToUpsert.add(qli);
                }
                
                // 执行DML
                try {
                    // 先upsert报价项
                    if (!qlisToUpsert.isEmpty()) {
                        upsert qlisToUpsert;
                        System.debug( '保存报价项数量: ' + qlisToUpsert.size());
                        
                        // 更新产品ID到报价项ID的映射
                        for (QuoteLineItem qli : qlisToUpsert) {
                            if (qli.Id != null && qli.Product2Id != null) {
                                productIdToNewQLIId.put(qli.Product2Id, qli.Id);
                                System.debug( '产品ID到报价项ID映射: ' + qli.Product2Id + ' -> ' + qli.Id);
                            }
                        }
                    }
                    
                    // 然后删除不再需要的报价项，但不删除产品组的产品
                    List<QuoteLineItem> qlisToDelete = new List<QuoteLineItem>();
                    for (QuoteLineItem qli : existingQLIList) {
                        if (!retainedQLIIds.contains(qli.Id) && qli.ISGROUP__c == false) {
                            qlisToDelete.add(qli);
                        }
                    }
                    
                    if (!qlisToDelete.isEmpty()) {
                        delete qlisToDelete;
                        System.debug( '删除报价项数量: ' + qlisToDelete.size());
                    }
                } catch (Exception e) {
                    System.debug( '保存报价项DML出错: ' + e.getMessage() + '\n' + e.getStackTraceString());
                    throw new AuraHandledException('保存报价项出错: ' + e.getMessage());
                }
                
                // 处理报价方式
                List<Quotation_Method__c> pmsToUpsert = new List<Quotation_Method__c>();
                List<Quotation_Method__c> pmsToDelete = new List<Quotation_Method__c>();
                
                // 处理每个产品的报价方式
                for (ProductWrapper pw : products) {
                    if (String.isBlank(pw.productId)) {
                        continue; // 跳过没有产品ID的数据
                    }
                    
                    String productId = pw.productId;
                    Id qliId = productIdToNewQLIId.get(productId);
                    
                    if (qliId == null) {
                        System.debug( '警告: 未找到产品的报价项ID: ' + productId);
                        continue; // 跳过没有有效报价项ID的产品
                    }
                    
                    System.debug( '处理产品报价方式 - 产品ID: ' + productId + ', 报价项ID: ' + qliId);
                    
                    // 获取现有报价方式
                    List<Quotation_Method__c> existingPMs = existingPricingMethods.containsKey(qliId) ? 
                        existingPricingMethods.get(qliId) : new List<Quotation_Method__c>();
                    
                    // 创建方法类型到报价方式的映射
                    Map<String, Quotation_Method__c> methodToPM = new Map<String, Quotation_Method__c>();
                    for (Quotation_Method__c pm : existingPMs) {
                        if (String.isNotBlank(pm.Method__c)) {
                            methodToPM.put(pm.Method__c, pm);
                        }
                    }
                    
                    // 标记所有现有报价方式为待删除
                    Set<Id> retainedPMIds = new Set<Id>();
                    
                    // 处理每个报价方式
                    if (pw.quoteTypes != null) {
                        System.debug('产品报价方式数量: ' + pw.quoteTypes.size());
                        
                        for (QuoteTypeWrapper qtw : pw.quoteTypes) {
                            if (String.isBlank(qtw.type)) {
                                System.debug( '跳过没有类型的报价方式');
                                continue; // 跳过没有类型的报价方式
                            }
                            
                            try {
                                Quotation_Method__c pm;
                                String methodType = getMethodTypeFromQuoteType(qtw.type);
                                System.debug('处理报价方式: ' + qtw.type + ' -> ' + methodType);
                                
                                // 查找现有报价方式
                                if (String.isNotBlank(methodType) && methodToPM.containsKey(methodType)) {
                                    pm = methodToPM.get(methodType);
                                    if (pm != null && pm.Id != null) {
                                        retainedPMIds.add(pm.Id);
                                        System.debug('更新现有报价方式: ' + pm.Id);
                                    }
                                } else {
                                    // 创建新报价方式
                                    pm = new Quotation_Method__c();
                                    pm.Quote_Line_Item_ID__c = qliId;
                                    pm.ISGROUP__c = false; // 确保新创建的报价方式不是产品组的
                                    System.debug( '创建新报价方式，类型: ' + methodType);
                                }
                                
                                // 根据类型设置字段
                                if (qtw.type == 'minimumUnitPrice') {
                                    pm.Method__c = '保底单价*保底数量';
                                    pm.Minimum_ProdUnitPrice__c = qtw.minimumUnitPrice != null ? qtw.minimumUnitPrice : 0;
                                    pm.Minimum_Amout__c = qtw.minimumQuantity != null ? qtw.minimumQuantity : 0;
                                    System.debug( '设置保底单价*保底数量 - 保底单价: ' + pm.Minimum_ProdUnitPrice__c + ', 保底数量: ' + pm.Minimum_Amout__c);
                                } else if (qtw.type == 'fixedUsage') {
                                    pm.Method__c = '固定用量*固定单价';
                                    pm.Fixed_Dosage__c = qtw.fixedUsage != null ? qtw.fixedUsage : 0;
                                    pm.Fixed_UnitPrice__c = qtw.fixedAmount != null ? qtw.fixedAmount : 0;
                                    System.debug( '设置固定用量*固定单价 - 固定用量: ' + pm.Fixed_Dosage__c + ', 固定单价: ' + pm.Fixed_UnitPrice__c);
                                } else if (qtw.type == 'maximumAmount') {
                                    pm.Method__c = '金额封顶';
                                    pm.Amount_Cap__c = qtw.maximumAmount != null ? qtw.maximumAmount : 0;
                                    System.debug('设置金额封顶 - 封顶金额: ' + pm.Amount_Cap__c);
                                } else if (qtw.type == 'productDiscount') {
                                    pm.Method__c = '产品折扣';
                                    pm.Discount_Factor__c = qtw.discountCoefficient != null ? qtw.discountCoefficient : 0;
                                    pm.Fixed_Rebate__c = qtw.fixedRebate != null ? qtw.fixedRebate : 0;
                                    pm.Cash_Reduce__c = qtw.cashReduce != null ? qtw.cashReduce : 0;
                                    pm.Credit__c = qtw.credit != null ? qtw.credit : 0;
                                    System.debug('设置产品折扣 - 折扣系数: ' + pm.Discount_Factor__c + ', 固定返利: ' + pm.Fixed_Rebate__c + ', 现金减免: ' + pm.Cash_Reduce__c + ', Credit: ' + pm.Credit__c);
                                }
                                
                                pmsToUpsert.add(pm);
                            } catch (Exception e) {
                                System.debug('处理报价方式出错: ' + e.getMessage() + '\n' + e.getStackTraceString());
                               
                            }
                        }
                    }
                    
                    // 添加要删除的报价方式
                    for (Quotation_Method__c pm : existingPMs) {
                        if (!retainedPMIds.contains(pm.Id)) {
                            pmsToDelete.add(pm);
                        }
                    }
                }
                
                // 执行DML
                try {
                    if (!pmsToDelete.isEmpty()) {
                        delete pmsToDelete;
                        System.debug( '删除报价方式数量: ' + pmsToDelete.size());
                    }
                    
                    if (!pmsToUpsert.isEmpty()) {
                        upsert pmsToUpsert;
                        System.debug( '保存报价方式数量: ' + pmsToUpsert.size());
                    }
                } catch (Exception e) {
                    System.debug( '保存报价方式DML出错: ' + e.getMessage() + '\n' + e.getStackTraceString());
                    throw new AuraHandledException('保存报价方式出错: ' + e.getMessage());
                }
                
                // 返回保存后的数据
                return getQuoteItemsWithPricingMethods(quoteId);
            } catch (Exception e) {
                System.debug( '处理报价项出错: ' + e.getMessage() + '\n' + e.getStackTraceString());
                throw new AuraHandledException('保存报价项出错: ' + e.getMessage());
            }
            
        } catch (Exception e) {
            System.debug('保存报价项总体出错: ' + e.getMessage() + '\n' + e.getStackTraceString());
            throw new AuraHandledException('保存报价项出错: ' + e.getMessage());
        }
    }
    
    /**
     * 根据前端类型获取后端Method__c字段值
     */
    private static String getMethodTypeFromQuoteType(String quoteType) {
        if (quoteType == 'minimumUnitPrice') {
            return '保底单价*保底数量';
        } else if (quoteType == 'fixedUsage') {
            return '固定用量*固定单价';
        } else if (quoteType == 'maximumAmount') {
            return '金额封顶';
        } else if (quoteType == 'productDiscount') {
            return '产品折扣';
        }
        return '';
    }
    
    /**
     * 自定义JSON解析方法
     */
    private static List<ProductWrapper> parseProductsJson(String jsonString) {
        List<ProductWrapper> result = new List<ProductWrapper>();
        
        if (String.isBlank(jsonString)) {
            return result;
        }
        
        try {
            // 先解析为通用JSON对象
            List<Object> jsonObjects = (List<Object>)JSON.deserializeUntyped(jsonString);
            System.debug( '解析到产品数量: ' + jsonObjects.size());
            
            if (jsonObjects == null || jsonObjects.isEmpty()) {
                System.debug('警告: 没有找到任何产品数据');
                return result;
            }
            
            // 处理每个产品对象
            for (Integer i = 0; i < jsonObjects.size(); i++) {
                try {
                    Object obj = jsonObjects[i];
                    if (obj == null) {
                        System.debug( '警告: 产品对象为null，索引: ' + i);
                        continue;
                    }
                    
                    // 将对象转换为Map
                    Map<String, Object> productMap = (Map<String, Object>)obj;
                    ProductWrapper pw = new ProductWrapper();
                    
                    // 设置基本属性
                    if (productMap.containsKey('productId')) {
                        pw.productId = String.valueOf(productMap.get('productId'));
                    }
                    if (productMap.containsKey('productName')) {
                        pw.productName = String.valueOf(productMap.get('productName'));
                    }
                    if (productMap.containsKey('customerAccountId')) {
                        pw.customerAccountId = String.valueOf(productMap.get('customerAccountId'));
                    }
                    if (productMap.containsKey('profitDescription')) {
                        pw.profitDescription = String.valueOf(productMap.get('profitDescription'));
                    }
                    
                    System.debug( '处理产品: ' + pw.productName + ' (ID: ' + pw.productId + ')');
                    
                    // 安全地设置数字字段
                    safelySetDecimal(productMap, 'unitPrice', pw);
                    safelySetDecimal(productMap, 'taxRate', pw);
                    
                    // 处理报价方式
                    if (productMap.containsKey('quoteTypes')) {
                        Object quoteTypesRawObj = productMap.get('quoteTypes');
                        if (quoteTypesRawObj == null) {
                            System.debug( '产品 ' + pw.productName + ' 的报价方式为null');
                            pw.quoteTypes = new List<QuoteTypeWrapper>();
                            result.add(pw);
                            continue;
                        }
                        
                        // 确保quoteTypes是数组
                        List<Object> quoteTypesObj;
                        try {
                            quoteTypesObj = (List<Object>)quoteTypesRawObj;
                            System.debug(LoggingLevel.INFO, '产品 ' + pw.productName + ' 有 ' + quoteTypesObj.size() + ' 个报价方式');
                            
                            for (Integer j = 0; j < quoteTypesObj.size(); j++) {
                                try {
                                    Object qtObj = quoteTypesObj[j];
                                    if (qtObj == null) {
                                        System.debug( '警告: 报价方式对象为null，产品: ' + pw.productName + ', 索引: ' + j);
                                        continue;
                                    }
                                    
                                    // 将报价方式对象转换为Map
                                    Map<String, Object> qtMap = (Map<String, Object>)qtObj;
                                    QuoteTypeWrapper qtw = new QuoteTypeWrapper();
                                    
                                    // 设置类型和标签
                                    if (qtMap.containsKey('type')) {
                                        qtw.type = String.valueOf(qtMap.get('type'));
                                    }
                                    if (qtMap.containsKey('label')) {
                                        qtw.label = String.valueOf(qtMap.get('label'));
                                    }
                                    
                                    System.debug( '处理报价方式: ' + qtw.type + ' - ' + qtw.label);
                                    
                                    // 根据类型设置相应的字段
                                    if (qtw.type == 'minimumUnitPrice') {
                                        safelySetQuoteTypeDecimal(qtMap, 'minimumUnitPrice', qtw);
                                        safelySetQuoteTypeDecimal(qtMap, 'minimumQuantity', qtw);
                                    } else if (qtw.type == 'fixedUsage') {
                                        safelySetQuoteTypeDecimal(qtMap, 'fixedUsage', qtw);
                                        safelySetQuoteTypeDecimal(qtMap, 'fixedAmount', qtw);
                                    } else if (qtw.type == 'maximumAmount') {
                                        safelySetQuoteTypeDecimal(qtMap, 'maximumAmount', qtw);
                                    } else if (qtw.type == 'productDiscount') {
                                        safelySetQuoteTypeDecimal(qtMap, 'discountCoefficient', qtw);
                                        safelySetQuoteTypeDecimal(qtMap, 'fixedRebate', qtw);
                                        safelySetQuoteTypeDecimal(qtMap, 'cashReduce', qtw);
                                        safelySetQuoteTypeDecimal(qtMap, 'credit', qtw);
                                    }
                                    
                                    // 添加到报价方式列表
                                    if (pw.quoteTypes == null) {
                                        pw.quoteTypes = new List<QuoteTypeWrapper>();
                                    }
                                    pw.quoteTypes.add(qtw);
                                } catch (Exception e) {
                                    System.debug( '处理报价方式时出错，产品: ' + pw.productName + ', 索引: ' + j + ', 错误: ' + e.getMessage() + '\n' + e.getStackTraceString());
                                    // 继续处理下一个报价方式
                                }
                            }
                        } catch (Exception e) {
                            System.debug( '处理报价方式数组出错，产品: ' + pw.productName + ', 错误: ' + e.getMessage() + '\n' + e.getStackTraceString());
                            pw.quoteTypes = new List<QuoteTypeWrapper>();
                        }
                    } else {
                        pw.quoteTypes = new List<QuoteTypeWrapper>();
                    }
                    
                    result.add(pw);
                } catch (Exception e) {
                    System.debug( '处理产品对象出错，索引: ' + i + ', 错误: ' + e.getMessage() + '\n' + e.getStackTraceString());
                    // 继续处理下一个产品
                }
            }
            
            System.debug('成功解析产品数量: ' + result.size());
            return result;
        } catch (Exception e) {
            System.debug( 'JSON解析出错: ' + e.getMessage() + '\n' + e.getStackTraceString());
            // 返回空列表
            return new List<ProductWrapper>();
        }
    }
    
    /**
     * 安全设置ProductWrapper的Decimal字段
     */
    private static void safelySetDecimal(Map<String, Object> sourceMap, String fieldName, ProductWrapper target) {
        if (!sourceMap.containsKey(fieldName) || sourceMap.get(fieldName) == null) {
            // 字段不存在或为null，使用默认值0
            if (fieldName == 'unitPrice') {
                target.unitPrice = 0;
            } else if (fieldName == 'taxRate') {
                target.taxRate = 0;
            }
            return;
        }
        
        try {
            String valueStr = String.valueOf(sourceMap.get(fieldName));
            if (String.isNotBlank(valueStr)) {
                Decimal decimalValue = Decimal.valueOf(valueStr);
                
                // 直接设置字段值
                if (fieldName == 'unitPrice') {
                    target.unitPrice = decimalValue;
                } else if (fieldName == 'taxRate') {
                    target.taxRate = decimalValue;
                }
                
                System.debug( '设置产品字段 ' + fieldName + ' = ' + decimalValue);
            }
        } catch (Exception e) {
            System.debug( '设置Decimal字段出错: ' + fieldName + ', 错误: ' + e.getMessage());
            // 使用默认值
            if (fieldName == 'unitPrice') {
                target.unitPrice = 0;
            } else if (fieldName == 'taxRate') {
                target.taxRate = 0;
            }
        }
    }
    
    /**
     * 安全设置QuoteTypeWrapper的Decimal字段
     */
    private static void safelySetQuoteTypeDecimal(Map<String, Object> sourceMap, String fieldName, QuoteTypeWrapper target) {
        if (!sourceMap.containsKey(fieldName) || sourceMap.get(fieldName) == null) {
            // 字段不存在或为null，使用默认值
            setDefaultValueForField(fieldName, target);
            return;
        }
        
        try {
            String valueStr = String.valueOf(sourceMap.get(fieldName));
            if (String.isNotBlank(valueStr)) {
                Decimal decimalValue = Decimal.valueOf(valueStr);
                
                // 直接设置字段值
                if (fieldName == 'minimumUnitPrice') {
                    target.minimumUnitPrice = decimalValue;
                } else if (fieldName == 'minimumQuantity') {
                    target.minimumQuantity = decimalValue;
                } else if (fieldName == 'fixedUsage') {
                    target.fixedUsage = decimalValue;
                } else if (fieldName == 'fixedAmount') {
                    target.fixedAmount = decimalValue;
                } else if (fieldName == 'maximumAmount') {
                    target.maximumAmount = decimalValue;
                } else if (fieldName == 'discountCoefficient') {
                    target.discountCoefficient = decimalValue;
                } else if (fieldName == 'fixedRebate') {
                    target.fixedRebate = decimalValue;
                } else if (fieldName == 'cashReduce') {
                    target.cashReduce = decimalValue;
                } else if (fieldName == 'credit') {
                    target.credit = decimalValue;
                }
                
                System.debug( '设置报价方式字段 ' + fieldName + ' = ' + decimalValue);
            }
        } catch (Exception e) {
            System.debug( '设置报价方式Decimal字段出错: ' + fieldName + ', 错误: ' + e.getMessage());
            // 使用默认值
            setDefaultValueForField(fieldName, target);
        }
    }
    
    //初始化
    private static void setDefaultValueForField(String fieldName, QuoteTypeWrapper target) {
        if (fieldName == 'minimumUnitPrice') {
            target.minimumUnitPrice = 0;
        } else if (fieldName == 'minimumQuantity') {
            target.minimumQuantity = 0;
        } else if (fieldName == 'fixedUsage') {
            target.fixedUsage = 0;
        } else if (fieldName == 'fixedAmount') {
            target.fixedAmount = 0;
        } else if (fieldName == 'maximumAmount') {
            target.maximumAmount = 0;
        } else if (fieldName == 'discountCoefficient') {
            target.discountCoefficient = 0;
        } else if (fieldName == 'fixedRebate') {
            target.fixedRebate = 0;
        } else if (fieldName == 'cashReduce') {
            target.cashReduce = 0;
        } else if (fieldName == 'credit') {
            target.credit = 0;
        }
    }
    
    /**
     * 产品包装类
     */
    public class ProductWrapper {
        @AuraEnabled public String productId;
        @AuraEnabled public String productName; // 添加产品名称字段
        @AuraEnabled public Decimal unitPrice;
        @AuraEnabled public Decimal taxRate;
        @AuraEnabled public String customerAccountId;
        @AuraEnabled public String profitDescription;
        @AuraEnabled public List<QuoteTypeWrapper> quoteTypes;
        
        public ProductWrapper() {
            this.quoteTypes = new List<QuoteTypeWrapper>();
            this.unitPrice = 0;
            this.taxRate = 0;
        }
    }
    
    /**
     * 报价方式包装类
     */
    public class QuoteTypeWrapper {
        @AuraEnabled public String type;
        @AuraEnabled public String label;
        
        // 保底单价*保底数量
        @AuraEnabled public Decimal minimumUnitPrice;
        @AuraEnabled public Decimal minimumQuantity;
        
        // 固定用量*固定金额
        @AuraEnabled public Decimal fixedUsage;
        @AuraEnabled public Decimal fixedAmount;
        
        // 金额封顶
        @AuraEnabled public Decimal maximumAmount;
        
        // 产品折扣
        @AuraEnabled public Decimal discountCoefficient;
        @AuraEnabled public Decimal fixedRebate;
        @AuraEnabled public Decimal cashReduce;
        @AuraEnabled public Decimal credit;
        
        public QuoteTypeWrapper() {
            this.minimumUnitPrice = 0;
            this.minimumQuantity = 0;
            this.fixedUsage = 0;
            this.fixedAmount = 0;
            this.maximumAmount = 0;
            this.discountCoefficient = 0;
            this.fixedRebate = 0;
            this.cashReduce = 0;
            this.credit = 0;
        }
    }
}