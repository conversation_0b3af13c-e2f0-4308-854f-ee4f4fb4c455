public class Interface_PaymentObject {
    public String uuid;
    public String znumb;
    public String sysid;
    public List<Header> header;
    
    public class Header {
        public String vbeln; //发票编号
        public String fkart; //发票类型
        public String kunnr; //客户编码
        public String fkdat; //开票日期
        public String erdat; //创建日期<->SFDC月权月日期
        public String bukrs; //公司代码
        public String zterm; //收付条件
        public String waerk; //凭证币种
        public String netwr; //总竞价
        public String mwsbk; //总税额
        public String zvbelv;//SAP合同编号
        public String bstkd; //SF合同号
        public String zexrate;//汇率
        public String zconcur;//合同币种
        public String augru_auft;//订单原因
        public List<Item> item;
    }
    
    public class Item {
        public String zitem; //行项目唯一标识
        public String posnr; //发票行项目
        public String matnr; //物料编号
        public String fkimg; //数量
        public String meins; //计量单位
        public String znetwr;//行项目净价
        public String mwsbp; //行项目税额
        public String kbetr; //税率
    }
    
}