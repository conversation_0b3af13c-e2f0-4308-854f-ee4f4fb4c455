public with sharing class PaymentNotificationPDFOverseaController {
    public Map<String, PaymentItem> returnItems;  
    public PaymentHead headInfo{ get; set; }
    public String recordId{ get; set; }
    public PaymentNotificationPDFOverseaController() {
        // 从URL参数获取记录ID
        recordId = ApexPages.currentPage().getParameters().get('id');
        

        //结构数据

        returnItems = new Map<String,PaymentItem>();//行项目
        headInfo = new PaymentHead();
        // 查询相关数据
        if (String.isNotBlank(recordId)) {
            //TODO： 查询数据，构建展示数据
            //付款通知书
            generateHeadInfo();
            
        }
    }
    public void generateItem(){
        
    }

    public void generateHeadInfo(){
        BillPaymentAdvice__c paymentRecord = [SELECT Id,Name,
                                                     Account__c,Account__r.Name,Account__r.CompanyAddress__c,
                                                     Account__r.Customer_ICP_Number__c,
                                                     Account__r.Tax_Categroy__c,
                                                     Account__r.TAX_Num1__c,
                                                     Contract_Number__c,
                                                     StartDate__c,
                                                     EndDate__c,
                                                     paymentType__c,
                                                     Billing_Number__c,
                                                     Expense_Settlement_Method__c, 
                                                     Contract_Currency__c,
                                                     PaymentCycle__c,
                                                     PlanNo__c,
                                                     PayableAmount__c,
                                                     Payment_Currency__c,
                                                     Exchange_Rate__c,
                                                     Due_Date__c,
                                                     Adjustment_Note__c
                                              FROM BillPaymentAdvice__c 
                                              WHERE Id = :recordId];
        //客户银行信息
        BankInfo__c bankInfo = [SELECT Id,Account__c,Name,Account__r.Name,Bank_Swift_Code__c,Bank_Key__c  
                                FROM BankInfo__c 
                                WHERE Account__c = :paymentRecord.Account__c limit 1]; 
        //联系人信息
        Contact con = [SELECT Id,Name,AccountId,Contact_Type__c,Position__c,Email,MobilePhone FROM Contact WHERE Contact_Type__c ='账单' AND AccountId = :paymentRecord.Account__c LIMIT 1];
        headInfo.company = paymentRecord.Account__r.Name;
        headInfo.companyAddress = paymentRecord.Account__r.CompanyAddress__c;
        headInfo.registrationNo = paymentRecord.Account__r.Customer_ICP_Number__c;
        headInfo.GSTRegistrationNo = paymentRecord.Account__r.TAX_Num1__c;
        headInfo.taxType = paymentRecord.Account__r.Tax_Categroy__c;
        headInfo.contactPerson = con.Name;
        headInfo.position = con.Position__c;
        headInfo.tel = con.MobilePhone;
        headInfo.email = con.Email;
        headInfo.billingNum = paymentRecord.Billing_Number__c;

        headInfo.due_date = paymentRecord.Due_Date__c; //应付款日期
        headInfo.payableAmount = paymentRecord.PayableAmount__c;  //应付金额
        headInfo.payment_Currency = paymentRecord.Payment_Currency__c;

        headInfo.beneficiaryName = bankInfo.Account__r.Name;
        headInfo.beneficiaryBankName = bankInfo.Name;
        headInfo.beneficiaryAddress = paymentRecord.Account__r.CompanyAddress__c;
        headInfo.beneficiaryACNo = bankInfo.Bank_Key__c;
        headInfo.SWIFT_Code = bankInfo.Bank_Swift_Code__c;
    }

    public static String wrapLongData(String text, Integer lineLength){
        if (text == null) return '';
    
        // 中文等不需要空格分隔的语言
        Pattern chinesePattern = Pattern.compile('[\\u4e00-\\u9fa5]');
        Matcher matcher = chinesePattern.matcher(text);
        if (matcher.find()) {
            return text.replaceAll('(.{' + lineLength + '})', '$1\n');
        }
        // 英文等需要按单词换行的语言
        else {
            return wrapByWords(text, lineLength);
        }
    }
    public static String wrapByWords(String text, Integer maxLineLength) {
        if (text == null || text.length() <= maxLineLength) {
            return text;
        }
        
        Pattern p = Pattern.compile('.{1,' + maxLineLength + '}(\\s+|$)|\\S+?(\\s+|$)');
        Matcher m = p.matcher(text);
        List<String> lines = new List<String>();
        
        while (m.find()) {
            lines.add(m.group().trim());
        }
        String returnStr = String.join(lines, '\n');
        System.debug(returnStr);
        return returnStr;
    }
    
    public class PaymentItem{
        public String productName{get; set;}
        public String configuration{get; set;}
        public String productProperty{get; set;}
        public String type{get; set;}
        public Decimal paymentQuantity{get; set;}
        public String region{get; set;}
        public Date startDate{get; set;}
        public Date endDate{get; set;}
        public Decimal unitPrice_includeTax{get; set;}
        public Decimal unitPrice{get; set;}
        public Decimal price_IncludeTax{get; set;}
        public Decimal price{get; set;}
    }

    public class PaymentHead{
        public String company{get; set;} 
        public String companyAddress{get; set;} 
        public String registrationNo{get; set;} 
        public String taxType{ get; set;}
        public String GSTRegistrationNo{ get; set;}
        public String contactPerson{ get; set;}  
        public String position{ get; set;} 
        public String tel{ get; set;}  
        public String email{ get; set;}  
        public String billingNum{ get; set;}  

        public String payment_Currency{ get; set;} //结算币种
        public Date due_date{ get; set;} //应付款日期
        public Decimal payableAmount{ get; set;}  //应付金额

        public String beneficiaryName{get; set;}
        public String beneficiaryBankName{get; set;}
        public String beneficiaryAddress{get; set;}
        public String beneficiaryACNo{get; set;}
        public String SWIFT_Code{get; set;}
    }
}