/**
 * Author: Dean
 * Date: 2025-07-010
 * Description: 入站接口执行器
 * 1. 负责根据传入的参数动态调用指定的入站接口实现类，并记录接口调用日志
 * 2. 统一处理接口执行结果的返回结构
 * 
 * Test Class: Interface_InboundExecutorTest
 * Change Log:
 * 2025-07-10: Created
 */
public without sharing class Interface_InboundExecutor {
    public Interface_InboundParam interfaceParam {get; set;}
    public Interface_Log__c interfaceLog {get; set;}
    public Map<String, Object> interfaceInboundResultMap;

    public Interface_InboundExecutor(Interface_InboundParam param,Interface_Log__c log) {
        interfaceInboundResultMap = new Map<String, Object>();
        interfaceInboundResultMap.put('code', '200');
        interfaceInboundResultMap.put('message', 'success');

        if (param.interfaceName == null || param.executeInboundClassName == null || param.dataString == null) {
            interfaceInboundResultMap.put('code', 'E-01');
            interfaceInboundResultMap.put('message','缺少执行关键配置信息，请联系管理员');
        }
        
        this.interfaceParam = param;
        this.interfaceLog = log;
    }

    public Map<String, Object> execute(){
        interfaceLog.CallTime__c = System.now();
        interfaceLog.RequestBody__c = interfaceParam.dataString;
        interfaceLog.InterfaceName__c = interfaceParam.interfaceName;
        interfaceLog.RequestHeader__c = interfaceParam.requestHeader;


        if (interfaceParam.executeInboundClassName != null) {
            Type t = Type.forName(interfaceParam.executeInboundClassName);
            Interface_InboundBase baseInfo = (Interface_InboundBase)t.newInstance();
            interfaceInboundResultMap = baseInfo.execute(interfaceParam);
            String responseBody = JSON.serialize(interfaceInboundResultMap);
            if (String.isNotBlank(interfaceParam.successFlag) && responseBody.contains(interfaceParam.successFlag)) {
                interfaceLog.Status__c = 'Success';
                interfaceLog.ResponseBody__c = responseBody;
            } else {
                interfaceLog.ErrorMessage__c	 = responseBody;
                interfaceLog.Status__c = 'Failed';
            }
        
            interfaceLog.ResponseBody__c = JSON.serialize(interfaceInboundResultMap);
            CWUtility.insertLog(new List<Interface_Log__c>{interfaceLog});
        }
        return interfaceInboundResultMap;
    }
}