/**
 * Author: Dean
 * Date: 2025-07-09
 * Description: 出站接口执行器
 * 1. 负责根据传入的参数组装出站请求，发送HTTP请求，并记录接口调用日志
 * 2. 支持自定义请求头、请求体、重试机制等功能
 * 
 * Test Class: Interface_OutboundExecutorTest
 * Change Log:
 * 2025-07-09: Created
 */
public without sharing class Interface_OutboundExecutor {
    public String dataString {get;set;}
	public Interface_Log__c logInfo {get;set;}
    public Interface_OutboundParam param {get;set;}
    public Integer retryCount = 0;

    public Interface_OutboundExecutor(Interface_OutboundParam param) {
        logInfo = new Interface_Log__c();
        logInfo.InterfaceName__c = param.interfaceName;
        logInfo.CallTime__c = System.now();
        logInfo.InterfaceType__c = 'outbound';
        this.param = param;
        if (param.dataObject != null) {
            dataString = JSON.serialize(param.dataObject);
        } else if (param.dataString != null) {
            dataString = param.dataString;
        }
        logInfo.Endpoint__c = param.endpoint;
        logInfo.RequestHeader__c = param.requestHeader;
        logInfo.RequestBody__c = dataString;
        if (param.recordIdSet != null && param.recordIdSet.size() > 0) {
            logInfo.RecordIDs__c = String.join(param.recordIdSet, ';');
        }
    }

    public String execute(){
        Integer timeout = 120000;
        String headerStr = param.requestHeader;
        Map<String, String> headerMap = new Map<String, String>();
        for(String str : headerStr.split(';')){
            headerMap.put(str.substringBefore('='),str.substringAfter('='));
        }
        String responseBody;
        try{
            Http http = new Http();
            HttpRequest httpRequest = new HttpRequest();  
            httpRequest.setMethod('POST');
            httpRequest.setTimeout(timeout);
            httpRequest.setEndpoint(param.endpoint);  
            
            if(headerMap != null && headerMap.size() > 0){
                for(String headerParam : headerMap.keySet()){
                    httpRequest.setHeader(headerParam, headerMap.get(headerParam));
                }
            }
            httpRequest.setBody(dataString);
            //if(param.httpMethod == 'POST' && dataString != null){
                    
            //}

            // if(param.httpMethod == 'GET'){
            //     httpRequest.setEndpoint(param.endpoint + dataString); 
            // }

            
            HttpResponse httpResponse = new HttpResponse();
            if (!Test.isRunningTest()){
                httpResponse = http.send(httpRequest);
            }
            if (httpResponse != null && httpResponse.getBody() != null) {
                responseBody = httpResponse.getBody();
            }

            if (responseBody.contains('timed out') 
                && param.retryTimes != null 
                && retryCount < Integer.valueOf(param.retryTimes)) {
                retryCount ++;
                execute();
            }
            
            logInfo.ResponseBody__c = responseBody;

            if (String.isNotBlank(httpRequest.getEndpoint())){
                logInfo.RequestHeader__c = httpRequest.getEndpoint();
            }

            if (String.isNotBlank(httpRequest.getBody())){
                logInfo.RequestBody__c = httpRequest.getBody();
            }


            //目前sap统一的返回样式
            Map<String,Object> responseMap = new Map<String,Object>();
            responseMap = (Map<String,Object>)JSON.deserializeUntyped(responseBody);
            if(responseMap.containsKey('msgty') && responseMap.get('msgty') != null){
                String resultStr = ((String) responseMap.get('msgty')).toUpperCase();
                if(resultStr == 'S'){
                    logInfo.Status__c = 'Success';
                }else{
                    logInfo.ErrorMessage__c = responseBody;
                    logInfo.Status__c = 'Failed';
                }
            }
            // else if (String.isNotBlank(param.SuccessFlag) && responseBody.contains(param.SuccessFlag)) {
            //     logInfo.Status__c = 'Success';
            // } else {
            //     logInfo.ErrorMessage__c = responseBody;
            //     logInfo.Status__c = 'Failed';
            // }
            

        } catch (Exception e) {
            System.debug('excetipn'+e.getMessage());
            String message = e.getMessage();
            if (message.contains('timed out') 
                && param.retryTimes != null 
                && retryCount < Integer.valueOf(param.retryTimes)) {
                retryCount ++;
                execute();
            }

            logInfo.ErrorMessage__c = message;
            logInfo.Status__c = 'Failed';
        }

        if (param.isSaveLog) {
            CWUtility.insertLog(new List<Interface_Log__c>{logInfo});
        }

        
        return responseBody;
    }
}