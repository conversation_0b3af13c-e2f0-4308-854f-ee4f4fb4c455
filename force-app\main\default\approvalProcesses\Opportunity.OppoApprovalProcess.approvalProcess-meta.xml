<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>Customer_Name__c</field>
        <field>CompanySize__c</field>
        <field>BelongArea__c</field>
        <field>Primary_Product__c</field>
        <field>Secondary_Product__c</field>
        <field>Estimated_Signing_Date__c</field>
        <field>Estimated_Monthly_Right__c</field>
        <field>Is_Test__c</field>
        <field>Estimated_Sales_Amount__c</field>
        <field>Presales_Personnel__c</field>
        <field>FollowUp_Plan__c</field>
        <field>OppoContent__c</field>
        <field>MSP_RemarkContent__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <label>商务经理</label>
        <name>BusinessApprovalProcess</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Product_ProductManager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <label>产品经理</label>
        <name>MSPProductApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Account.Is_Agent__c</field>
                <operation>equals</operation>
                <value>真</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>业务合作部</label>
        <name>BusinessCooApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>AreaManager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <label>区域经理</label>
        <name>AreaManagerApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Account_AreaManager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>TEXT(BelongArea__c)  &lt;&gt;   TEXT(Account.Customer_Location_Region__c)</formula>
        </entryCriteria>
        <label>跨区经理</label>
        <name>ApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <emailTemplate>unfiled$public/Request_Approve</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Opportunity.ApprovalStatus__c</field>
            <operation>contains</operation>
            <value>Draft,Rejected</value>
        </criteriaItems>
        <criteriaItems>
            <field>Opportunity.StageName</field>
            <operation>equals</operation>
            <value>Draft</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>OpportunityApproved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Request_be_Approved</name>
            <type>Alert</type>
        </action>
        <action>
            <name>UpdateStatus_OPP</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>OpportunityRejected</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Request_be_Rejected</name>
            <type>Alert</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>NotificationOpportunirySubmitForApproval</name>
            <type>Alert</type>
        </action>
        <action>
            <name>OpportunitySubmittedForApproval</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>商机报备审批流程</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>OpportunityBeDraft</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
