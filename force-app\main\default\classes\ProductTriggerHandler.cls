public with sharing class ProductT<PERSON>ger<PERSON>andler extends Trigger<PERSON>andler{
    Set<String> contractStatusSet = new Set<String>{'InProgress','AutoRenewal','Amendment​'};
    public ProductTriggerHandler() {
        super('Product2');
   
    }
    public override void doAfterInsert(List<SObject> newList) {
       Service_Product.createPricebookEntries(newList);
       createNewAIContractProducts(newList);
    }

    //当SAP推送新AI模型服务产品时，创建合同调整单记录并将合同产品创建挂到对应合同上
    private void createNewAIContractProducts(List<Product2> products) {
        Set<Id> productIdSet =  new Set<Id>();
        List<Contract_Change_Request__c> newAIProductRequests = new List<Contract_Change_Request__c>();
        String productNameStr ='';
        for(Product2 product : products) {
            if(product.LevelCode2__c =='0203') {//AI模型服务
                productIdSet.add(product.id);
                productNameStr += product.Name + ';';
            }
        }
        if(!productIdSet.isEmpty()) {
            for(Contract AICon:[SELECT id,Product_Category__c,Service_End__c FROM Contract WHERE Product_Category__c ='P2' and SubscribeNewProducts__c = true and Contract_Status__c IN:contractStatusSet]){
                Contract_Change_Request__c ccr = new Contract_Change_Request__c();
                ccr.Contract__c = AICon.id;
                ccr.RecordTypeId = Schema.SObjectType.Contract_Change_Request__c.getRecordTypeInfosByDeveloperName().get('NewAIProducts').getRecordTypeId();
                ccr.ApprovalStatus__c ='Approved';
                ccr.Note__c = 'SAP推送新AI模型服务：'+productNameStr;
                ccr.AIProductIDs__c =  String.join(new List<Id>(productIdSet),';');
                newAIProductRequests.add(ccr);
            }
        }

        if(!newAIProductRequests.isEmpty()) {
            insert newAIProductRequests;
        }

    }


}