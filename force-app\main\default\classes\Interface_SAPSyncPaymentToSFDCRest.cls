@RestResource(urlMapping='/SyncPayment/*')
global with sharing class Interface_SAPSyncPaymentToSFDCRest {
    @HttpPost
    global static void Interface_SAPSyncPaymentToSFDCRest() {
        RestRequest req = RestContext.request;
        Interface_Log__c log = new Interface_Log__c();
        
        // 1. 解析请求数据
        Interface_InboundParam params = new Interface_InboundParam();
        params.requestHeader = req.headers.toString();
        params.dataString =  req.requestBody.toString();
        System.debug(params.dataString);
        params.interfaceName = 'Interface_SAPSyncPaymentToSFDCRest';
        params.executeInboundClassName = 'Interface_SAPSyncPaymentToSFDC';
        Interface_InboundExecutor executor= new Interface_InboundExecutor(params,log);
        executor.execute();  
    }
}