@RestResource(urlMapping='/SyncProductAndHierarchy/*')
global with sharing class Interface_SAPSyncProductToSFDCRest {
    @HttpPost
    global static void Interface_SAPSyncProductToSFDCRest() {
        RestRequest req = RestContext.request;
        Interface_Log__c log = new Interface_Log__c();
        
        // 1. 解析请求数据
        Interface_InboundParam params = new Interface_InboundParam();
        params.requestHeader = req.headers.toString();
        System.debug(params.requestHeader);
        params.dataString =  req.requestBody.toString();
        params.interfaceName = 'Interface_SAPSyncProductToSFDCRest';
        params.executeInboundClassName = 'Interface_SAPSyncProductToSFDC';
        Interface_InboundExecutor executor= new Interface_InboundExecutor(params,log);
        System.debug(params.dataString);
        executor.execute();  
    }
}