public with sharing class UpdatePaymentPlanQueue implements Queueable {
    private Set<id> ChangeContractIds;
    public UpdatePaymentPlanQueue(Set<id> ChangeContractIds) {
        this.ChangeContractIds = ChangeContractIds;
    }

    public void execute(QueueableContext context) {
        List<Contract_Change_Request__c> paymentPlanByTermination = new List<Contract_Change_Request__c>();
        List<Contract_Change_Request__c> paymentPlanByAutoRenewal = new List<Contract_Change_Request__c>();
        List<Contract_Change_Request__c> paymentPlanByAddAIProduct = new List<Contract_Change_Request__c>();
        for(Contract_Change_Request__c ccr:[SELECT id,ContractOldEndDate__c,NewEndDate__c FROM Contract_Change_Request__c WHERE Id IN:ChangeContractIds and ApprovalStatus__c='Approved']){
            if(ccr.RecordTypeNameForApprove__c == 'ContractTermination' && ccr.ContractOldEndDate__c > ccr.NewEndDate__c){//提前终止
                paymentPlanByTermination.add(ccr);
            }else if(ccr.RecordTypeNameForApprove__c == 'ContractRenew'){
                paymentPlanByAutoRenewal.add(ccr);
            }else if(ccr.RecordTypeNameForApprove__c == 'NewAIProducts'){
                paymentPlanByAddAIProduct.add(ccr);
            }
        }
        if(!paymentPlanByTermination.isEmpty()){
            updatePaymentPlanByTermination(paymentPlanByTermination);
        }
        if(!paymentPlanByAutoRenewal.isEmpty()){
            updatePaymentPlanByAutoRenew(paymentPlanByAutoRenewal);
        }
        if(!paymentPlanByAddAIProduct.isEmpty()){
            updatePaymentPlanByAddAIProduct(paymentPlanByAddAIProduct);
        }
    }

    public void updatePaymentPlanByTermination(List<Contract_Change_Request__c> newRequests) {
        Set<id> contractids = new Set<id>();
        map<id,Date> newDateMap =new map<id,Date>();
        for(Contract_Change_Request__c ccr:newRequests){
            contractids.add(ccr.Contract__c);
            newDateMap.put(ccr.Contract__c,ccr.NewEndDate__c);
        }
        List<CollectionPlan__c> collectionPlan = [Select id,(Select id,Period_StartDate__c,Period_EndDate__c,InActive__c from CollectionPlans__r) FROM CollectionPlan__c WHERE Contract__c IN:contractids];
        List<CollectionPlanLine__c> updateCollectionPlanLineList = new List<CollectionPlanLine__c>();
        for(CollectionPlan__c plan:collectionPlan){
            Date newEndDate = newDateMap.get(plan.Contract__c);
            Date endDate = plan.Service_EndDate__c;
            if(newEndDate == null || newEndDate >= endDate) continue;
            Date startDate = plan.Service_StartDate__c;
            for(CollectionPlanLine__c detail:plan.CollectionPlans__r){
                if(detail.Period_StartDate__c > newEndDate){//在新的结束日期之后的收款计划明细
                    detail.InActive__c = true;
                    updateCollectionPlanLineList.add(detail);
                }else if(detail.Period_StartDate__c < newEndDate && detail.Period_EndDate__c > newEndDate){//新的结束日期在某一期中间
                    detail.Period_EndDate__c = newEndDate;
                    detail.isChanged__c = true;
                    updateCollectionPlanLineList.add(detail);
                }
            }  
        }
        if(!updateCollectionPlanLineList.isEmpty()){
            update updateCollectionPlanLineList;
        }

    }

    public void updatePaymentPlanByAutoRenew(List<Contract_Change_Request__c> newRequests) {
        list<CollectionPlanLine__c> result = new list<CollectionPlanLine__c>();
        List<CollectionPlanLine__c> updateCollectionPlanLineList = new List<CollectionPlanLine__c>();
        Set<id> contractids = new Set<id>();
        map<id,Date> newDateMap =new map<id,Date>();
        for(Contract_Change_Request__c ccr:newRequests){
            contractids.add(ccr.Contract__c);
            newDateMap.put(ccr.Contract__c,ccr.NewEndDate__c);
        }
        List<CollectionPlan__c> collectionPlan = [Select id,(Select id,PlanNo__c,Period_StartDate__c,Period_EndDate__c,InActive__c from CollectionPlans__r ORDER BY PlanNo__c desc) FROM CollectionPlan__c WHERE Contract__c IN:contractids];
        
        for(CollectionPlan__c plan:collectionPlan){
            Date newEndDate = newDateMap.get(plan.Contract__c);
            Integer i;//最后一期的期数
            String periodType = plan.PaymentCycle__c;
            String expenseType = plan.Expense_Settlement_Method__c;
            if(expenseType == 'Installment') continue;//分期 再自动续约的场景没有涉及
            CollectionPlanLine__c lastPeriod = plan.CollectionPlans__r[0];
            i = Integer.valueOf(lastPeriod.PlanNo__c);
            Date lstcurrentStart = lastPeriod.Period_StartDate__c;
            if (periodType == 'byMonth') {
                lastPeriod.Period_EndDate__c = Date.newInstance(lstcurrentStart.year(), lstcurrentStart.month(), 1)
                    .addMonths(1).addDays(-1);
            } else if (periodType == 'byQuarter') {
                Integer monthIndex = lstcurrentStart.month() - 1;
                Integer mod3 = monthIndex - (Math.floor(monthIndex / 3) * 3).intValue();
                Integer addMonths = 3 - mod3;
                lastPeriod.Period_EndDate__c = Date.newInstance(lstcurrentStart.year(), lstcurrentStart.month(), 1)
                    .addMonths(addMonths).addDays(-1);
            } else if (periodType == 'byHalfYear') {
                Integer monthIndex = lstcurrentStart.month() - 1;
                Integer mod6 = monthIndex - (Math.floor(monthIndex / 6) * 6).intValue();
                Integer addMonths = 6 - mod6;
                lastPeriod.Period_EndDate__c = Date.newInstance(lstcurrentStart.year(), lstcurrentStart.month(), 1)
                    .addMonths(addMonths).addDays(-1);
            } else if (periodType == 'byYear') {
                lastPeriod.Period_EndDate__c = Date.newInstance(lstcurrentStart.year() + 1, 1, 1).addDays(-1);
            } else {
                throw new IllegalArgumentException('周期参数无效：'+periodType);
            }
            if (lastPeriod.Period_EndDate__c  > newEndDate) {
                lastPeriod.Period_EndDate__c = newEndDate;
            }
            updateCollectionPlanLineList.add(lastPeriod);

            //生成新的收款计划明细数据
            Date startDate = lastPeriod.Period_EndDate__c.addDays(1);
            Date currentStart = startDate;
            if (startDate == null || newEndDate == null || startDate > newEndDate) {
                system.debug('无需生成新的收款计划明细');
                continue;
            }
            while (currentStart <= newEndDate) {
                Date currentEnd;
                i++;
                if (periodType == 'byMonth') {
                    currentEnd = Date.newInstance(currentStart.year(), currentStart.month(), 1)
                        .addMonths(1).addDays(-1);
                } else if (periodType == 'byQuarter') {
                    Integer monthIndex = currentStart.month() - 1;
                    Integer mod3 = monthIndex - (Math.floor(monthIndex / 3) * 3).intValue();
                    Integer addMonths = 3 - mod3;
                    currentEnd = Date.newInstance(currentStart.year(), currentStart.month(), 1)
                        .addMonths(addMonths).addDays(-1);
                } else if (periodType == 'byHalfYear') {
                    Integer monthIndex = currentStart.month() - 1;
                    Integer mod6 = monthIndex - (Math.floor(monthIndex / 6) * 6).intValue();
                    Integer addMonths = 6 - mod6;
                    currentEnd = Date.newInstance(currentStart.year(), currentStart.month(), 1)
                        .addMonths(addMonths).addDays(-1);
                } else if (periodType == 'byYear') {
                    currentEnd = Date.newInstance(currentStart.year() + 1, 1, 1).addDays(-1);
                } else {
                    throw new IllegalArgumentException('周期参数无效：'+periodType);
                }
                // 不能超过总的endDate
                if (currentEnd > newEndDate) {
                    currentEnd = newEndDate;
                }

                CollectionPlanLine__c cpl = new CollectionPlanLine__c();
                cpl.Period_StartDate__c = currentStart;
                cpl.Period_EndDate__c = currentEnd;
                cpl.CollectionPlan__c = plan.id;
                cpl.ContractId__c = plan.Contract__c;
                cpl.ExternalId__c = plan.id + '_' + String.valueof(currentEnd.year()) + '_' + String.valueof(currentEnd.Month());
                cpl.Description__c = '第 ' + i +  ' 期收款计划明细';
                cpl.PlanNo__c = i;
                result.add(cpl);

                currentStart = currentEnd.addDays(1);
            }

            if(updateCollectionPlanLineList.size() > 0){
                update updateCollectionPlanLineList;
            }
            if(result.size() > 0){
                insert result;
            }      
        }
    }

    public void updatePaymentPlanByAddAIProduct(List<Contract_Change_Request__c> newRequests) {
        Set<id> contractids = new Set<id>();
        map<id,List<Contract_Product__c>> contractProductMap = new map<id,List<Contract_Product__c>>();
        List<CollectionPlanLineProduct__c> planLineProductList = new List<CollectionPlanLineProduct__c>();
        map<id,Set<Id>> aiProductIdsContractMap = new map<id,Set<Id>>();
        for(Contract_Change_Request__c ccr:newRequests){
            if(ccr.AIProductIDs__c != null || ccr.AIProductIDs__c != '')continue;
            contractids.add(ccr.Contract__c);
            Set<Id> aiProductIdSet = new Set<Id>();
            for(String str:ccr.AIProductIDs__c.split(';')){
                aiProductIdSet.add(str);
            }
            aiProductIdsContractMap.put(ccr.Contract__c,aiProductIdSet);
        }
        List<CollectionPlan__c> collectionPlan = [Select id,(Select id,PlanNo__c,Period_StartDate__c,Period_EndDate__c,InActive__c from CollectionPlans__r ORDER BY PlanNo__c desc) FROM CollectionPlan__c WHERE Contract__c IN:contractids];
        List<Contract> contractList =  [Select id,Number_of_Installments__c,Total_Installment_Amount__c,FirstPayment__c,DepositAmount__c,Service_Start__c,Service_End__c,Expense_Settlement_Method__c,Payment_Cycle__c,(Select id,Product__c,ValidFrom__c,ValidTo__c from Contract_Products__r) FROM contract where id IN :contractIds];
        for(CollectionPlan__c cp:collectionPlan){
            for(CollectionPlanLine__c cpl:cp.CollectionPlans__r){
                if(cpl.isSpecial__c)continue;
                List<CollectionPlanLineProduct__c> returnProdList = getCollectionPlanLineProduct(cpl,contractProductMap,aiProductIdsContractMap);
                planLineProductList.addAll(returnProdList);
            }
        }
        if(planLineProductList.size() > 0){
            insert planLineProductList;
        }
    }

    public List<CollectionPlanLineProduct__c> getCollectionPlanLineProduct(CollectionPlanLine__c planLine,map<id,List<Contract_Product__c>> contractProductMap,map<id,Set<Id>> aiProductIdsContractMap){
        List<CollectionPlanLineProduct__c> result = new List<CollectionPlanLineProduct__c>();
        String contractId = planLine.ContractId__c;
        if(contractId != null && contractProductMap.containsKey(contractId) ){
            List<Contract_Product__c> contractProductList = contractProductMap.get(contractId);
            Set<Id> aiProductIdSet = aiProductIdsContractMap.containsKey(contractId) ?aiProductIdsContractMap.get(contractId):new Set<Id>();
            
            for(Contract_Product__c cp:contractProductList){
                if(!aiProductIdSet.contains(cp.id)) continue;//只处理新增AI服务模型
                Date startDate = cp.ValidFrom__c;
                Date endDate = cp.ValidTo__c;
                if (startDate == null || endDate == null || startDate > endDate || planLine.Period_StartDate__c == null || planLine.Period_EndDate__c == null) {
                    throw new IllegalArgumentException('日期参数不合法');
                }
                CollectionPlanLineProduct__c lineProduct = new CollectionPlanLineProduct__c();
                lineProduct.CollectionPlanLine__c = planLine.Id;
                lineProduct.Product__c = cp.Product__c;
                lineProduct.ContractProduct__c = cp.id;
                lineProduct.ExternalId__c = String.valueOf(planLine.Id) + String.valueOf(cp.Product__c);
                if(startDate >= planLine.Period_StartDate__c && startDate <= planLine.Period_EndDate__c && endDate >= planLine.Period_StartDate__c && endDate <= planLine.Period_EndDate__c){
                    // ┌─────planLine区间A─────────┐
                    //      ┌─product区间B────┐
                    lineProduct.StartDate__c = startDate;
                    lineProduct.EndDate__c = endDate;
                    result.add(lineProduct);
                }else if( endDate >= planLine.Period_StartDate__c && endDate <= planLine.Period_EndDate__c && startDate < planLine.Period_StartDate__c  ){
                    //     ┌─────planLine区间A──────┐
                    //┌──────────product区间B────┐
                    lineProduct.StartDate__c = planLine.Period_StartDate__c;
                    lineProduct.EndDate__c = endDate;
                    result.add(lineProduct);
                }else if(startDate >= planLine.Period_StartDate__c && startDate <= planLine.Period_EndDate__c &&  endDate > planLine.Period_EndDate__c ){
                    // ┌─────planLine区间A──────┐
                    //      ┌─────product区间B───────┐
                    lineProduct.StartDate__c = startDate;
                    lineProduct.EndDate__c = planLine.Period_EndDate__c;
                    result.add(lineProduct);
                }else if(endDate >  planLine.Period_EndDate__c && startDate < planLine.Period_StartDate__c){
                    //      ┌──planLine区间A───┐
                    // ┌───────product区间B─────────┐
                    lineProduct.StartDate__c = planLine.Period_StartDate__c;
                    lineProduct.EndDate__c = planLine.Period_EndDate__c;
                    result.add(lineProduct);
                }else if(startDate > planLine.Period_EndDate__c || endDate < planLine.Period_StartDate__c){
                    // ┌──planLine区间A───┐
                    //                       ┌────product区间B────┐
                }
            }
        }

        return result;
    }
}