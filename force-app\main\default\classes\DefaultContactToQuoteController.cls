public with sharing class DefaultContactToQuoteController {
    public DefaultContactToQuoteController() {

    }

    //获取客户的甲方联系人信息
   @AuraEnabled(cacheable=true)
     public static Map<String, Object> getOpportunityAndContactsData(String recordId) {
        Map<String, Object> result = new Map<String, Object>();
        
        try {
            // 查询商机信息
            Opportunity opp = [
                SELECT AccountId, Primary_Product__c, 
                       Contract_Start_Date__c, CloseDate
                FROM Opportunity 
                WHERE Id = :recordId
                LIMIT 1
            ];
            
            // 将商机信息添加到结果中
            result.put('accountId', opp.AccountId);
            result.put('primaryProduct', opp.Primary_Product__c);
            result.put('contractStartDate', opp.Contract_Start_Date__c);
            result.put('closeDate', opp.CloseDate);
            
            // 查询该客户下的联系人
            List<Contact> contacts = [
                SELECT Id, Name, Position__c 
                FROM Contact 
                WHERE AccountId = :opp.AccountId
                ORDER BY Name
                LIMIT 1000
            ];
            
            result.put('contacts', contacts);
            
            return result;
        } catch (Exception e) {
            throw new AuraHandledException('获取数据失败: ' + e.getMessage());
        }
    }
}