public without sharing class QuoteToContractController {
    // 存储当前报价记录
    // public Quote currentQuote {get; set;}
    
    // // 构造方法：通过报价ID获取当前报价数据
    // public QuoteToContractController(ApexPages.StandardController stdController) {
    //     // 从页面传入的报价ID加载记录（需包含所有要映射的字段）
    //     currentQuote = [SELECT Id, Name,AccountId,Expense_Settlement_Method__c, Payment_Way__c, Payment_Cycle__c, Is_Deposit__c,
    //                     DepositAmount__c,prepayment__c,Payment_Settlement_Method_Description__c,Number_of_Installments__c,
    //                     Total_Installment_Amount__c,Cucstomer_Contract__c,Customer_Finance_Contact__c,Customer_Tech_Contact__c,
    //                     MSP_Contract_Contact__c,MSP_Technical_Contact__c,MSP_Finance_Contact__c,StartDate__c,EndDate__c,ExchangeRateIdentification__c,
    //                     Non_Standard_Rate__c,Product_Cate__c,Contract_Cur__c,Settlement_Cur__c,Add_Type__c,
    //         (SELECT Account_ID__c, UnitPrice, Tax_Rate__c,Profit_Statement__c,Description,
    //          QuoteLineStartDate__c,QuoteLineEndDate__c,Product2Id,Product2.Name,LineNum__c,Region__c FROM QuoteLineItems) 
    //         FROM Quote WHERE Id = :stdController.getId()];
    // }

    @AuraEnabled // 将报价转化为合同
    public static Map<String,String> convertToContract(String recordId){
        Map<String,String> result = new Map<String,String>();
        result.put('errormessage', '');
        result.put('contractId', '');
        
        try {
            Quote currentQuote = [SELECT Id, Name,AccountId,Expense_Settlement_Method__c, Payment_Way__c, Payment_Cycle__c, Is_Deposit__c,
                        DepositAmount__c,prepayment__c,Payment_Settlement_Method_Description__c,Number_of_Installments__c,
                        Total_Installment_Amount__c,Cucstomer_Contract__c,Customer_Finance_Contact__c,Customer_Tech_Contact__c,
                        MSP_Contract_Contact__c,MSP_Technical_Contact__c,MSP_Finance_Contact__c,StartDate__c,EndDate__c,ExchangeRateIdentification__c,
                        Non_Standard_Rate__c,Product_Cate__c,Contract_Cur__c,Settlement_Cur__c,Add_Type__c,TaxCode__c,PartyB_Signing_Company__c,
            (SELECT Account_ID__c, UnitPrice, Tax_Rate__c,Profit_Statement__c,Description,Product_Group__c,ISGROUP__c,
             QuoteLineStartDate__c,QuoteLineEndDate__c,Product2Id,Product2.Name,LineNum__c,Region__c FROM QuoteLineItems) 
            FROM Quote WHERE Id = :recordId];
           

            Map<String, Id> contractRecordTypeMap = new Map<String, Id>();
            contractRecordTypeMap.put('合同变更', Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractAmendment') != null ? Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractAmendment').getRecordTypeId() : null);
            contractRecordTypeMap.put('合同续签', Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractRenewal') != null ? Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractRenewal').getRecordTypeId() : null);
            contractRecordTypeMap.put('新合同', Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('NewContract') != null ? Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('NewContract').getRecordTypeId() : null);
            // 1. 创建合同对象并映射字段
            Contract newContract = new Contract();
            newContract.AccountId = currentQuote.AccountId; // 关联账户
            newContract.Expense_Settlement_Method__c = currentQuote.Expense_Settlement_Method__c; // 费用结算方式
            newContract.Payment_Method__c = currentQuote.Payment_Way__c; // 付款方式
            newContract.Payment_Cycle__c = currentQuote.Payment_Cycle__c; // 付款周期
            newContract.Is_Deposit__c = currentQuote.Is_Deposit__c == 'true' ? true : false; // 是否定金
            newContract.DepositAmount__c = currentQuote.DepositAmount__c; // 定金金额
            newContract.FirstPayment__c = currentQuote.prepayment__c; // 预付款
            newContract.Payment_Settlement_Method_Description__c = currentQuote.Payment_Settlement_Method_Description__c; // 付款结算方式描述
            newContract.Number_of_Installments__c = currentQuote.Number_of_Installments__c; // 分期数
            newContract.Total_Installment_Amount__c = currentQuote.Total_Installment_Amount__c; // 分期总金额
            newContract.PartyA_Contract_Owner__c = currentQuote.Cucstomer_Contract__c; // 客户合同
            newContract.PartyA_Finance_Owner__c = currentQuote.Customer_Finance_Contact__c; // 客户财务联系人
            newContract.PartyA_Tech_Owner__c = currentQuote.Customer_Tech_Contact__c; // 客户技术联系人
            newContract.PartyB_Contract_Owner__c = currentQuote.MSP_Contract_Contact__c; // MSP合同联系人
            newContract.PartyB_Tech_Owner__c = currentQuote.MSP_Technical_Contact__c; // MSP技术联系人
            newContract.PartyB_Finance_Owner__c = currentQuote.MSP_Finance_Contact__c; // MSP财务联系人
            newContract.Service_Start__c = currentQuote.StartDate__c; // 合同开始日期
            newContract.Service_End__c = currentQuote.EndDate__c; // 合同结束日期
            newContract.ExchangeRateIdentification__c = currentQuote.ExchangeRateIdentification__c; // 汇率识别
            newContract.Non_Standard_Rate__c = String.valueOf(currentQuote.Non_Standard_Rate__c); // 非标准费率
            newContract.Product_Category__c = currentQuote.Product_Cate__c; // 产品类别
            newContract.ContractCurrency__c = currentQuote.Contract_Cur__c; // 合同币种
            newContract.SettlementCurrency__c = currentQuote.Settlement_Cur__c; // 结算币种
            newContract.TaxCode__c = currentQuote.TaxCode__c;// 税码
            newContract.PartyB_Signing_Company__c = currentQuote.PartyB_Signing_Company__c;// 乙方主体
            newContract.Contract_Stage__c = 'Draft';
            newContract.Contract_Status__c = 'Draft';
            // 设置合同记录类型
            Id recordTypeId = null;
            if (currentQuote.Add_Type__c != null && contractRecordTypeMap.containsKey(currentQuote.Add_Type__c)) {
                recordTypeId = contractRecordTypeMap.get(currentQuote.Add_Type__c);
            }
            if (recordTypeId == null) {
                recordTypeId = contractRecordTypeMap.get('新合同'); // 默认新合同
            }
            newContract.RecordTypeId = recordTypeId;

            newContract.Name = currentQuote.Name; // 合同名称
            // 关联报价
            newContract.Quote__c = currentQuote.Id;

            // 先插入合同，拿到Id
            insert newContract;

            // 处理报价行项对合同产品行Contract_Product__c
            List<Contract_Product__c> contractLineItems = new List<Contract_Product__c>();
            if (currentQuote.QuoteLineItems != null) {
                for (QuoteLineItem qli : currentQuote.QuoteLineItems) {
                    Contract_Product__c cli = new Contract_Product__c();
                    cli.CustomerIDs__c = qli.Account_ID__c; // 关联账户
                    cli.UnitPrice__c = qli.UnitPrice; // 单价
                    cli.Tax_Rate__c = qli.Tax_Rate__c; // 税率
                    cli.Profit_Statement__c = qli.Profit_Statement__c; // 利润报表
                    cli.Description__c = qli.Description; // 描述
                    cli.ValidFrom__c = qli.QuoteLineStartDate__c; // 报价行开始日期
                    cli.ValidTo__c = qli.QuoteLineEndDate__c; // 报价行结束日期
                    cli.Product__c = qli.Product2Id; // 产品ID
                    cli.ContractLineItemNo__c = qli.LineNum__c; // 行号
                    cli.ProductArea__c = qli.Region__c; // 区域
                    cli.Contract__c = newContract.Id; // 关联合同ID
                    cli.Name = (qli.Product2 != null) ? qli.Product2.Name : '';
                    cli.Quote_Line_Item__c = qli.Id;
                    cli.Product_Group__c = qli.Product_Group__c;
                    cli.ISGROUP__c = qli.ISGROUP__c;
                    // 将报价行项添加到合同行项列表
                    contractLineItems.add(cli);
                }
            }
            //保存合同产品行
            if (!contractLineItems.isEmpty()) {
                insert contractLineItems;
            }

            // 处理分期期数对象：查询所有与该QuoteId相关的分期期数记录，并更新合同字段
            updateInstallmentItemsWithContract(currentQuote.Id, newContract.Id);

            // result.put('errormessage', '');
            result.put('contractId', newContract.Id);
            // 成功后跳转到新创建的合同详情页
            // return new PageReference('/' + newContract.Id);
            
        } catch (Exception e) {
            // ApexPages.addMessage(new ApexPages.Message(ApexPages.Severity.ERROR, '转化失败：' + e.getMessage()));
            result.put('errormessage', '转化失败：' + e.getMessage() +'; 报错行数：'+e.getLineNumber());

            // return null;
        }
        return result;
    }

    /**
     * 更新分期期数对象的合同字段
     * @param quoteId 报价ID
     * @param contractId 合同ID
     */
    private static void updateInstallmentItemsWithContract(Id quoteId, Id contractId) {
        System.debug('输入参数 - quoteId: ' + quoteId);
        System.debug('输入参数 - contractId: ' + contractId);

        try {
            // 查询所有与该QuoteId相关的分期期数记录
            List<Installments_Item__c> installmentItems = [
                SELECT Id,Name, Quote__c, Contract__c, InstallNo__c, Amount__c
                FROM Installments_Item__c
                WHERE Quote__c = :quoteId
            ];

            System.debug('查询到的分期期数记录数量: ' + installmentItems.size());
            System.debug('分期期数记录详情: ' + installmentItems);

            if (!installmentItems.isEmpty()) {
                // 更新所有分期期数记录的合同字段
                for (Installments_Item__c item : installmentItems) {
                    System.debug('更新分期期数记录 - ID: ' + item.Id + ', 第' + item.InstallNo__c + '期, 原合同字段: ' + item.Contract__c);
                    item.Contract__c = contractId;
                    System.debug('已设置新合同字段: ' + contractId);
                }

                // 批量更新分期期数记录
              
                update installmentItems;
                

            } else {
                System.debug('没有找到与QuoteId相关的分期期数记录');
            }

        } catch (Exception e) {
            System.debug('更新分期期数记录失败: ' + e.getMessage());
            System.debug('错误堆栈: ' + e.getStackTraceString());
        }

    }
}