public with sharing class DataProcessTool {
    //格式化日期，输入20250712，输出日期格式
    public static Date parseSAPDate(String sapDate) {
        if (sapDate == null || sapDate == '99991231') {
            return null;
        }
        Integer year = Integer.valueOf(sapDate.substring(0, 4));
        Integer month = Integer.valueOf(sapDate.substring(4, 6));
        Integer day = Integer.valueOf(sapDate.substring(6, 8));
        return Date.newInstance(year, month, day);
    }

    public static String formatSAPDate(Date inputDate) {
        if (inputDate == null) {
            return '';
        }
        // 将日期格式 2025-07-24 转换为 20250724
        String yearStr = String.valueOf(inputDate.year());
        String monthStr = String.valueOf(inputDate.month());
        if (monthStr.length() == 1) monthStr = '0' + monthStr;
        String dayStr = String.valueOf(inputDate.day());
        if (dayStr.length() == 1) dayStr = '0' + dayStr;
        return yearStr + monthStr + dayStr;
    }
}