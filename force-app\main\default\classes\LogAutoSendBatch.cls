global class LogAutoSendBatch implements Database.Batchable<sObject>, Database.AllowsCallouts, Database.Stateful {

    private String typeNFM;
    private String messageGroupNumber;
    private Datetime times;
    private Boolean isForecast=false;
    private String loginId;

     //计划的作业优化  一小时两次 start
     // private BatchEmailUtil.ScBean scB1;
     // 计划的作业优化  一小时两次 end

    
    global LogAutoSendBatch() {
    }
    global LogAutoSendBatch(String loginId) {
        this.loginId = loginId;
    }
   
    global LogAutoSendBatch(String type,String message) {
        this.typeNFM = type;
        this.messageGroupNumber = message;
        System.debug('type++++++'+type + 'message+++++' + message);
    }
  
    global LogAutoSendBatch(String type,Boolean isForecast) {
        this.typeNFM = type;
        this.isForecast = isForecast;
    }
   
    global LogAutoSendBatch(String type, String message, Datetime times) {
        this.typeNFM = type;
        this.messageGroupNumber = message;
        this.times = times;
    }
   
    // public Integer max_cnt = Integer.valueOf(System.Label.batch_retry_max_cnt);
    global Database.QueryLocator start(Database.BatchableContext BC) {
        System.debug('Starting batch process...');
        // 查询所有未关闭的机会记录作为批处理数据源
        return Database.getQueryLocator([SELECT Id, Name, StageName FROM Opportunity WHERE IsClosed = false]);
    }
        
 

    // 实现 execute 方法
    global void execute(Database.BatchableContext BC, List<SObject> scope) {
        for (SObject record : scope) {
            // 在这里处理每个批次中的记录
            System.debug('Processing record: ' + record);
            // 示例：可以根据记录类型进行不同处理
            if (record.getSObjectType() == Opportunity.SObjectType) {
                Opportunity opp = (Opportunity) record;
                // 处理机会记录的逻辑
            }
        }
    }

    global void finish(Database.BatchableContext BC) {
        if(String.isNotBlank(typeNFM) && typeNFM == 'NFM010' && isForecast){
            // Id execBTId = Database.executeBatch(new BeforeSSOpportunityBatch(), 100);
        }
    }
}