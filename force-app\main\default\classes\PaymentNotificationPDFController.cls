public with sharing class PaymentNotificationPDFController {
    public Map<String, List<BillPaymentAdviceDetail__c>> returnItems{ get; set; }
    public PaymentHead headInfo{ get; set; }
    public Boolean hasItems{ get; set; }
    String recordId;
    List<BillPaymentAdviceDetail__c> details;
    public PaymentNotificationPDFController() {
        // 从URL参数获取记录ID
        recordId = ApexPages.currentPage().getParameters().get('id');
        

        //结构数据

        returnItems = new Map<String,List<BillPaymentAdviceDetail__c>>();//行项目
        headInfo = new PaymentHead();
        // 查询相关数据
        if (String.isNotBlank(recordId)) {
            //付款通知书
            generateHeadInfo();
            hasItems = false;
            //付款通知书明细
            details = [SELECT Id,BillPaymentAdvice__c,
                            Product__c,Product__r.Name, Product__r.productCode, Product_Code__c, Unit__c,
                            Quantity__c, UnitPrice__c, UnitPrice_tax__c, 
                            Total_Amount__c, Total_Amount_Tax__c, Tax__c, 
                            Tax_Rate__c, Area__c, PaymentType__c,  
                            ProductDetailProperty__c,
                            Configuration__c,Service_StartDate__c,
                            Service_EndDate__c   
                        FROM BillPaymentAdviceDetail__c 
                        WHERE BillPaymentAdvice__c =:recordId 
                        AND Product__c != NULL 
                        ORDER BY Product__c];
            if (!details.isEmpty()) {
                hasItems = true;
                generateItem();
            }
        }
    }

    public void generateItem(){

        //productCode,性质，付款计划明细
        for (BillPaymentAdviceDetail__c detail : details) {
            if (returnItems.containsKey(detail.Product__r.productCode)) {
                List<BillPaymentAdviceDetail__c> tempItems  = returnItems.get(detail.Product__r.productCode);
                tempItems.add(detail);
                returnItems.put(detail.Product__r.productCode,tempItems);
            } else {
                List<BillPaymentAdviceDetail__c> tempItems = new List<BillPaymentAdviceDetail__c>();
                tempItems.add(detail);
                returnItems.put(detail.Product__r.productCode, tempItems);
            }
        }
        for (String productCode : returnItems.keySet()) {
            Decimal totalAmount = 0;
            Decimal totalTax = 0;
            for (BillPaymentAdviceDetail__c detail : returnItems.get(productCode)) {
                totalAmount+= detail.Total_Amount__c==null?0:detail.Total_Amount__c;
                totalTax=detail.Tax__c==null?0:detail.Tax__c;
            }
            BillPaymentAdviceDetail__c totalDetail = new BillPaymentAdviceDetail__c();
            totalDetail.PaymentType__c = '本项合计';
            totalDetail.Total_Amount__c = totalAmount;
            totalDetail.Tax__c = totalTax;
            List<BillPaymentAdviceDetail__c> tempItems = returnItems.get(productCode);
            tempItems.add(totalDetail);
            returnItems.put(productCode,tempItems);
        }
        System.debug(returnItems);
        
    }

    public void generateHeadInfo(){
        BillPaymentAdvice__c paymentRecord = [SELECT Id,Name,
                                                     Account__c,Account__r.Name,
                                                     Contract_Number__c,
                                                     StartDate__c,
                                                     EndDate__c,
                                                     paymentType__c,
                                                     Expense_Settlement_Method__c, 
                                                     Contract_Currency__c,
                                                     PaymentCycle__c,
                                                     PlanNo__c,
                                                     PayableAmount__c,
                                                     Payment_Currency__c,
                                                     Exchange_Rate__c,
                                                     Due_Date__c,
                                                     Adjustment_Note__c
                                              FROM BillPaymentAdvice__c 
                                              WHERE Id = :recordId];
            //客户银行信息
        BankInfo__c bankInfo = [SELECT Id,Account__c,Name,Bank_Swift_Code__c 
                                FROM BankInfo__c 
                                WHERE Account__c = :paymentRecord.Account__c limit 1]; 
        headInfo.startDate =  paymentRecord.StartDate__c; //本期开始日期
        headInfo.endDate = paymentRecord.EndDate__c; //本期结束日期
        headInfo.paymentNo = paymentRecord.Name; //单据编号-付款通知书编号
        headInfo.accountName = paymentRecord.Account__r.Name;
        headInfo.accountBankName = bankInfo.Bank_Swift_Code__c;  //开户行名称
        headInfo.bankAccount = bankInfo.Name; //银行账号
        headInfo.orderNo = paymentRecord.Contract_Number__c;  //订单编号-合同号
        headInfo.paymentType = paymentRecord.paymentType__c;  //收入类型
        headInfo.contractCurrency = paymentRecord.Contract_Currency__c;  //合同币种
        headInfo.paymentMethod = paymentRecord.Expense_Settlement_Method__c;  //付款方式
        headInfo.paymentCycle = paymentRecord.PaymentCycle__c;  //付款周期
        headInfo.paymentPeriod = paymentRecord.PlanNo__c;  //付款期数
        headInfo.payableAmount = paymentRecord.PayableAmount__c;  //应付金额
        headInfo.payment_Currency = paymentRecord.Payment_Currency__c; //结算币种
        headInfo.payment_exchangeRate = paymentRecord.Exchange_Rate__c; //结算汇率
        headInfo.due_date = paymentRecord.Due_Date__c; //应付款日期
        headInfo.adjustment_Note = wrapLongData(paymentRecord.Adjustment_Note__c,30);//调整备注
    }

    public static String wrapLongData(String text, Integer lineLength){
        if (text == null) return '';
    
        // 中文等不需要空格分隔的语言
        Pattern chinesePattern = Pattern.compile('[\\u4e00-\\u9fa5]');
        Matcher matcher = chinesePattern.matcher(text);
        if (matcher.find()) {
            return text.replaceAll('(.{' + lineLength + '})', '$1\n');
        }
        // 英文等需要按单词换行的语言
        else {
            return wrapByWords(text, lineLength);
        }
    }
    public static String wrapByWords(String text, Integer maxLineLength) {
        if (text == null || text.length() <= maxLineLength) {
            return text;
        }
        
        Pattern p = Pattern.compile('.{1,' + maxLineLength + '}(\\s+|$)|\\S+?(\\s+|$)');
        Matcher m = p.matcher(text);
        List<String> lines = new List<String>();
        
        while (m.find()) {
            lines.add(m.group().trim());
        }
        String returnStr = String.join(lines, '\n');
        System.debug(returnStr);
        return returnStr;
    }
    
    public class PaymentItem{
        public String productName{get; set;}
        public String configuration{get; set;}
        public String productProperty{get; set;}
        public String type{get; set;}
        public Decimal paymentQuantity{get; set;}
        public String region{get; set;}
        public Date startDate{get; set;}
        public Date endDate{get; set;}
        public Decimal unitPrice_includeTax{get; set;}
        public Decimal unitPrice{get; set;}
        public Decimal price_IncludeTax{get; set;}
        public Decimal price{get; set;}
    }

    public class PaymentHead{
        public Date startDate{get; set;} //本期开始日期
        public Date endDate{get; set;}  //本期结束日期
        public String paymentNo{get; set;}  //单据编号-付款通知书编号
        public String accountName{get; set;} //客户名称
        
        public String accountBankName{get; set;}  //开户行名称
        public String bankAccount{get; set;} //银行账号
        public String orderNo{get; set;}  //订单编号-合同号
        public String paymentType{get; set;}  //收入类型
        public String contractCurrency{get; set;}  //合同币种
        public String paymentMethod{get; set;}  //付款方式
        public String paymentCycle{get; set;}  //付款周期
        public Decimal paymentPeriod{get; set;}  //付款期数
        public Decimal payableAmount{get; set;}  //应付金额
        public String payment_Currency{get; set;} //结算币种
        public Decimal payment_exchangeRate{get; set;} //结算汇率
        public Date due_date{get; set;} //应付款日期
        public String adjustment_Note{get; set;}//调整备注
    }

}