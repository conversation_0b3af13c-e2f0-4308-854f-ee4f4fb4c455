public with sharing class Interface_CRMSyncAccountToSAP {

    public static Interface_OutboundParam param {get;set;}

    public Interface_CRMSyncAccountToSAP() {
    
   }

    @future(callout=true)
    public static void doSyncAccountToSAP(Map<Id,String> AccountMap) {
        param = new Interface_OutboundParam();
        param.interfaceName = 'CRMSyncAccountToSAP';
        param.endpoint ='callout:SAP_API_Credential'; //改动
        param.retryTimes = 3;
        param.recordIdSet = AccountMap.keySet();
        param.targetObject = 'Account';
        // param.httpMethod = 'POST';
        // String token = '';
      
        param.requestHeader = getRequestHeader();
        param.dataString = getRequestBody(AccountMap);      

        Interface_OutboundExecutor syncContract = new Interface_OutboundExecutor(param);
        String body = syncContract.execute();
        String accountId = '';
        for(String accId : accountMap.keySet()){
            accountId = accId;
        }
        
        System.debug('accId**:'+accountId);
        updateAccount(body,accountId);
        System.debug('body'+body);
        
    }

    public static void updateAccount(String body,String accId){
        // String body ='{"msgty":"S","msgtx":"客户1000041创建成功","msgid":"ZSD01","msgno":"001","uuid":"e26eae9ad58027dbf952e3908644a31a","sapnum":"SH2","field1":"[{\"kunnr\":\"**********\"}]","field2":"**********","field3":"D5E5927ED9971FE099D5C4D07A31D7C4"}';
        String bodystr = body.replace('\\','').replace('"[','[').replace(']"',']');
        System.debug('bodystr: ' + bodystr);
        Map<String,Object > rawObj = (Map<String, Object>) JSON.deserializeUntyped(bodystr);
        if(rawObj.get('msgty')=='S'){
            // Get the value of 'field1' and deserialize it to a list of maps
            List<Object> field1List = (List<Object>)(rawObj.get('field1'));

            // Get the first map in the list and retrieve the value of 'kunnr'
            Map<String, Object> field1Map = (Map<String, Object>) field1List[0];
            String kunnrValue = String.valueOf(field1Map.get('kunnr'));
            if(String.isEmpty(kunnrValue)){
                return;
            }
            // Get the value of 'field2')
            Account acc = new Account();
            acc.Id = accId;
            acc.SAP_Num__c = kunnrValue;
            update acc;
        }
    }
      

    public static String getRequestHeader(){
        String encodedCredentials = CWUtility.getSAPEncodedCredentials();
        Map<String, String> headerMap = new Map<String, String>();
        headerMap.put('Content-Type', 'application/json');
        // headerMap.put('sap-client', '110'); //改动
        //headerMap.put('Authorization', 'Bearer '+token);
        //headerMap.put('Authorization', 'Basic ' + encodedCredentials);
        List<String> headerList = new List<String>();
        for (String key : headerMap.keySet()) {
            headerList.add(key + '=' + headerMap.get(key));
        }
        String headerString = String.join(headerList, ';');
        return headerString;
    }

     public static String getRequestBody(Map<Id,String> accountMap){
        // 查询Account对象并转化为JSon对象
        List<Account> accounts = [SELECT Id, Customer_Category__c, Name,Parent.Name,
            Customer_Industry__c, History_Account_Number__c, Street__c, PostalCode__c,CountryId__r.Country_Code__c,
            State__c,ProvinceId__r.Province_Code__c,  City__c, Tax_Categroy__c, TAX_Num1__c,CurrencyIsoCode,Payment_Terms__c,SAP_Num__c,
            Distribution_Channel__c,Customer_Location_Region__c,
            (select id,Product_Line__c,Sales_Status__c,SalesPerson__c, Product_Group_Control__c from ChildAccounts__r limit 1 ), 
            (select id,Name, BankContury__c,Bank_Swift_Code__c,Account_Holder_Name__c, Bank_Account_No__c, Bank_Reference__c, IBAN__c, Bank_Key__c from Bank_Infos_Child__r) 
            FROM Account where id IN :accountMap.keySet()];
        // List<AccountCreateWarp> accountCreateWarps = new List<AccountCreateWarp>();
        AccountCreateWarp accountCreateWarp = new AccountCreateWarp();
        for (Account acc : accounts) {
            
            Req req = new Req();
            req.NAME1 = acc.Name;//账号名称
            req.NAME4 = acc.Parent != null ? acc.Parent.Name : '';//父账号名称
            req.KTOKD = acc.Customer_Category__c==null?'':acc.Customer_Category__c;//客户类别
            req.TAXTYPE = acc.Tax_Categroy__c==null?'':acc.Tax_Categroy__c;
            req.TAXNUMXL = acc.TAX_Num1__c==null?'':acc.TAX_Num1__c;
            req.WAERS = acc.CurrencyIsoCode==null?'':acc.CurrencyIsoCode;
            req.ZTERM = acc.Payment_Terms__c==null?'':acc.Payment_Terms__c;
            req.VTWEG = acc.Distribution_Channel__c==null?'D1':acc.Distribution_Channel__c;
            req.BZIRK = acc.Customer_Location_Region__c==null?'': acc.Customer_Location_Region__c;//销售区域
            req.SORT1 = acc.Customer_Industry__c==null?'': acc.Customer_Industry__c;//客户行业
            req.BPEXT = acc.History_Account_Number__c==null?'':acc.History_Account_Number__c;//历史账号
            req.POST_CODE1 = acc.PostalCode__c==null?'':acc.PostalCode__c;//邮政编码
            req.LAND1 = acc.CountryId__r != null ? acc.CountryId__r.Country_Code__c : '';//国家代码
            req.CITY1 = acc.City__c ==null?'':acc.City__c;//城市
            req.REGION = acc.ProvinceId__r.Province_Code__c==null?'':acc.ProvinceId__r.Province_Code__c;//州
            req.STREET = acc.Street__c==null?'':acc.Street__c;//街道
            req.KUNNR = acc.SAP_Num__c==null?'':acc.SAP_Num__c;//SAP编号
            // 子表赋值
            for (Sales_Status__c childAcc : acc.ChildAccounts__r) {
                if(childAcc.Product_Line__c== accountMap.get(acc.Id)){
                    req.AUFSD = childAcc.Sales_Status__c==null?'':childAcc.Sales_Status__c;//销售状态
                    req.SALLER = childAcc.SalesPerson__c ==null?'':childAcc.SalesPerson__c;
                    req.SPART = childAcc.Product_Line__c==null?'':childAcc.Product_Line__c;//产品组
                    req.VKGRP = childAcc.Product_Group_Control__c ? '99':'';//产品组管控
                }
            }
            req.BANK = new List<BankObj>();
            for (BankInfo__c bank : acc.Bank_Infos_Child__r) {
                BankObj item = new BankObj();
                item.BANKS = bank.BankContury__c==null?'':bank.BankContury__c;
                item.BANKN = bank.Bank_Swift_Code__c==null?'':bank.Bank_Account_No__c;
                item.BKREF = bank.Bank_Reference__c==null?'':bank.Bank_Reference__c;
                item.IBAN = bank.IBAN__c==null?'':bank.IBAN__c;
                item.BANKL = bank.Name;
                req.BANK.add(item);
            }
            accountCreateWarp.req = req;
        }
        
        // accountCreateWarps.add(accountCreateWarp);
        String reqString = JSON.serialize(accountCreateWarp);
        system.debug(reqString);
        return reqString;
    }

    //定义AccountCreateWarp接口包装类
    public class AccountCreateWarp{
        public String uuid;//接口唯一标识
        public String znumb;//接口编号
        public String fsysid;//请求系统
        
        public Req req {get; set;}//请求体

        public AccountCreateWarp() {
            uuid = CWUtility.generateUUID();
            znumb = 'SD001';
            fsysid = 'SALESFORCE';
        }   
    }
    //request body
    public class Req {
        public String KTOKD {get; set;}//客户角色账户组 Customer_Category__c
        public String NAME1 {get; set;}//客户名称    Name
        public String NAME4 {get; set;}//客户集团   Parent.Name
        public String SORT1 {get; set;}//客户行业   Customer_Industry__c
        // public String SORT2 ='Sort2';
        public String BPEXT {get; set;}//历史客户编号History_Account_Number__c
        public String STREET {get; set;}//街道/门派Street__c
        public String POST_CODE1 {get; set;}//邮编PostalCode__c
        public String LAND1 {get; set;}//国家Country__c
        public String REGION {get; set;}//地区State__c
        public String CITY1 {get; set;}//城市City__c
        public String LANGU ='ZH';//默认值为中文
        public String TAXTYPE {get; set;} //税类别Tax_Categroy__c
        public String TAXNUMXL {get; set;}//税号：TAX_Num1__c
        public String WAERS {get; set;}//交易货币CurrencyIsoCode
        public String KALKS = '1';//客户定价过程
        public String ZTERM {get; set;}  //付款条款  Payment_Terms__c
        public String AKONT='**********';
        public String KONDA ='01';//定价组
        public String VTWEG {get; set;}//分销渠道Distribution_Channel__c
        public String AUFSD {get; set;}     //客户销售状态
        public String SPART {get; set;}   //产品组
        public String BZIRK {get; set;}//销售地区
        public String VKGRP {get; set;}//产品组管控
        public String SALLER {get; set;}
        public String KUNNR{get;set;} 
        public List<BankObj> BANK {get; set;}
        public Req() {
            BANK = new List<BankObj>();
        }          
    }

    public class BankObj {
            public String BANKS {get; set;}//银行国家
            public String BANKN {get; set;}//银行账号 Bank Account
            public String BKREF {get; set;}//参考Reference
            public String IBAN {get; set;}//IBAN
            public String BANKL {get; set;}//银行编号Bank Key
    }

    // public class AccountUpdateWrap{
    //    public String accountNumber {get; set;}//客户编号
    //    public String taxNum1 {get; set;}//税号
    //    public String productGroupControl {get; set;}//产品组管控
    //    public String productGroup {get; set;}//产品组
    //    public String salesStatusCode {get; set;}//销售状态

    // }


}