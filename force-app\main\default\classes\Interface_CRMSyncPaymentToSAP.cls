public without sharing class Interface_CRMSyncPaymentToSAP {
    public Interface_OutboundParam param {get;set;}
    Map<String, Object> requestBody = new Map<String, Object>();

    public Interface_CRMSyncPaymentToSAP(String BillPaymentAdviceId) {
        param = new Interface_OutboundParam();
        param.interfaceName = 'CRMSyncPaymentToSAP';
        param.endpoint ='callout:SAP_API_Credential';
        param.retryTimes = 3;
        param.recordIdSet = new Set<id>{BillPaymentAdviceId};
        param.targetObject = 'BillPaymentAdvice__c';
        
        //String token = '';
        param.requestHeader = getRequestHeader();
        param.dataString = getRequestBody(BillPaymentAdviceId);

        Interface_OutboundExecutor syncContract = new Interface_OutboundExecutor(param);
        String responseBody = syncContract.execute();
        system.debug('Response Body: ' + responseBody);
        // 解析SAP接口返回的数据
        if (String.isNotBlank(responseBody)) {
            try {
                // 假设返回为JSON格式
                Map<String, Object> respMap = (Map<String, Object>) JSON.deserializeUntyped(responseBody);
                String msgType = (respMap.containsKey('MSGTY') && respMap.get('MSGTY') != null) ? String.valueOf(respMap.get('MSGTY')) : '';
                String msgText = (respMap.containsKey('MSGTX') && respMap.get('MSGTX') != null) ? String.valueOf(respMap.get('MSGTX')) : '';
                // 会计凭证号码
                String DOCLN = (respMap.containsKey('DOCLN') && respMap.get('DOCLN') != null) ? String.valueOf(respMap.get('DOCLN')) : '';
                // 公司代码
                String BUKRS = (respMap.containsKey('BUKRS') && respMap.get('BUKRS') != null) ? String.valueOf(respMap.get('BUKRS')) : '';
                // 会计年度
                String GJAHR = (respMap.containsKey('GJAHR') && respMap.get('GJAHR') != null) ? String.valueOf(respMap.get('GJAHR')) : '';
                // 会计期间
                String MONAT = (respMap.containsKey('MONAT') && respMap.get('MONAT') != null) ? String.valueOf(respMap.get('MONAT')) : '';
                // 成功之后
                if(msgType =='S'){

                }

            } catch (Exception e) {
                throw new IllegalArgumentException('解析失败');
            }
        }

        
    }

    public String getRequestHeader(){
        String encodedCredentials = CWUtility.getSAPEncodedCredentials();
        Map<String, String> headerMap = new Map<String, String>();
        headerMap.put('Content-Type', 'application/json');
        //headerMap.put('sap-client', '100');
        //headerMap.put('Authorization', 'Bearer '+token);
        //headerMap.put('Authorization', 'Basic ' + encodedCredentials);
        List<String> headerList = new List<String>();
        for (String key : headerMap.keySet()) {
            headerList.add(key + '=' + headerMap.get(key));
        }
        String headerString = String.join(headerList, ';');
        return headerString;
    }

    public String getRequestBody(Id BillPaymentAdviceId){
        List<BillPaymentAdvice__c> BillPaymentAdviceList = [
            SELECT Id, OwnerId, Name, CurrencyIsoCode, RecordTypeId, CreatedDate, RecordType.Name,RecordType.developerName,
                   Billing_Number__c, Account_Number__c, Account__c, Billing_Date__c, Contract_Currency__c,SAP_Num__c,
                   Exchange_Rate__c, Contract_Number__c, SAP_Contract_Number__c, Total_Tax_Amount__c, Total_Amount__c,
                   Collection_Plan_Line__c, Company_Code__c, Account__r.Name, AccountBankName__c, BankAccount__c, Payment_Currency__c,
                   Billing_Type__c, Creat_Date__c, Expense_Settlement_Method__c, PaymentCycle__c, PlanNo__c,
                   PayableAmount__c, paymentType__c, Due_Date__c, StartDate__c, EndDate__c, Adjustment_Note__c 
            FROM BillPaymentAdvice__c 
            WHERE Id =:BillPaymentAdviceId];

        list<PaymentRequest> returnList = new list<PaymentRequest>();
        RequestBody body = new RequestBody();
        for(BillPaymentAdvice__c con : BillPaymentAdviceList){
            PaymentRequest pr = new PaymentRequest();
            pr.BUKRS = con.Company_Code__c;//公司代码

            pr.BUDAT = con.CreatedDate.date(); // 转换 DateTime 为 Date 类型
            pr.BLDAT = con.CreatedDate.date(); // 转换 DateTime 为 Date 类型

            // 获取月份并确保是两位数格式
            Integer monthNum = con.CreatedDate.month();
            pr.MONAT = monthNum < 10 ? '0' + String.valueOf(monthNum) : String.valueOf(monthNum);
            
            pr.WAERS = con.Payment_Currency__c;//动态值，账单的结算币种
            String paymentType = con.RecordType.developerName =='PrepaymentAdvice' ? 'A' : 'H';
            pr.item.add(new PaymentItem('15','001','H',con.Total_Amount__c+con.Total_Tax_Amount__c,'','','1001010000'));
            pr.item.add(new PaymentItem('09','002','S',con.Total_Amount__c+con.Total_Tax_Amount__c,paymentType,con.SAP_Num__c,''));
            body.req = pr;
        }
        String reqString = JSON.serialize(body);
        system.debug(reqString);
        return reqString;
    }
    public class RequestBody{
        public String UUID;//接口唯一标识
        public String ZNUMB;//接口编号
        public String fsysid;//请求系统
        public PaymentRequest req;
        public RequestBody(){
            UUID = CWUtility.generateUUID();
            ZNUMB = 'FI006';
            fsysid = 'POT';
            req = new PaymentRequest();
        }
    }

    public class PaymentRequest {
        public String BSTAT = ''; // 固定值，传空
        public String BELNR = ''; // 固定值，传空
        public String LDGRP = ''; // 固定值，传空
        public String BUKRS ; // 动态值，基于每个销售组织代码
        public String BLART = 'SA'; // 固定值，SA
        public Date BUDAT ; // 动态值，创建账单的日期
        public Date BLDAT ; // 动态值，创建账单的日期
        public String MONAT ; // 动态值，对应账单日期的月份
        public String WAERS ; // 动态值，账单的结算币种
        public String BKTXT = ''; // 固定值，传空
        public String XBLNR = 'Salesforce'; // 固定值，传Salesforce
        public String XREF2 = ''; // 固定值，为空
        public List<PaymentItem> item;
        public PaymentRequest(){
            item = new List<PaymentItem>();
        }
    } 

    public class PaymentItem {
        public String TYPE = 'AR'; // 类型
        public String BSCHL; // 过账码
        public String DOCLN; // 行项目号
        public String SHKZG; // 借/贷标识
        public String ACCOUNT ; // "供应商/客户号/总账科目"
        public String RACCT ; // 总账科目（备选统驭科目）
        public String UMSKZ; // 特别总账标志，预收A。押金H
        public Decimal TSL; // 凭证货币金额
        public String RSTGR = ''; // 原因代码
        public String RCNTR = ''; // 成本中心
        public String PRCTR = ''; // 利润中心
        public String OBJNR = ''; // 内部订单
        public String PROJK = ''; // wbs要素
        public String XNEGP = ''; // 反记账标识
        public String MWSKZ = ''; // 税码
        public String RASSC = ''; // 贸易伙伴
        public String ZTERM = ''; // 付款条款
        public String ZFBDT = ''; // 基准日期
        public String RFAREA = ''; // 功能范围
        public String ZUONR = ''; // 分配
        public String SGTXT = ''; // 行项目文本
        public String BANK = ''; // 出票银行代码
        public String ACCOU = ''; // 银行账户
        public PaymentItem(String BSCHL, String DOCLN, String SHKZG,Decimal TSL,String UMSKZ,String ACCOUNT,String RACCT) {
            this.BSCHL = BSCHL;
            this.DOCLN = DOCLN;
            this.SHKZG = SHKZG;
            this.TSL = TSL;
            this.UMSKZ = UMSKZ;
            this.ACCOUNT = ACCOUNT;
            this.RACCT = RACCT;
        }
    }


}