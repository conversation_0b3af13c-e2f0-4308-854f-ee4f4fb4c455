<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ComposeGmail</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ComposeGmail</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ComposeGmail</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>LogCall</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>LogCall</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>LogCall</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>MailMerge</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>MailMerge</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>MailMerge</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>RequestUpdate</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>RequestUpdate</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>RequestUpdate</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SendEmail</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SendEmail</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SendEmail</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ViewAll</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ViewAll</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ViewAll</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <enableFeeds>false</enableFeeds>
    <searchLayouts>
        <searchResultsAdditionalFields>TASK.SUBJECT</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>TASK.WHO_NAME</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>TASK.WHAT_NAME</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>TASK.DUE_DATE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CORE.USERS.ALIAS</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
