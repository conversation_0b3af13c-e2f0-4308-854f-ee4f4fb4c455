/**
 * Author: Dean
 * Date: 2025-07-08
 * Description: 触发器处理类
 * 1. 通过配置控制触发器是否开启
 * 2. 通过模板方法模式，让子类实现具体业务逻辑
 * 3. 子类需要实现doXXXXXXXX方法，方法名与触发器事件名相同
 * 
 * Test Class: TriggerHandlerTest
 * Change Log:
 * 2025-07-08: Created
 */
public abstract class TriggerHandler {
    protected Boolean isTriggerDisabled;

    public TriggerHandler(String objectApiName) {
        this.isTriggerDisabled = checkTriggerEnabled(objectApiName);
    }

    private Boolean checkTriggerEnabled(String objectApiName) {
        List<Trigger_Control__mdt> controls = [
            SELECT Disabled__c FROM Trigger_Control__mdt
            WHERE Object_API_Name__c = :objectApiName
            LIMIT 1
        ];
        return controls.isEmpty() ? false : controls[0].Disabled__c;
    }

    public void beforeInsert(List<SObject> newList) {
        if (isTriggerDisabled) return;
        this.doBeforeInsert(newList);
    }
    public void beforeUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        if (isTriggerDisabled) return;
        this.doBeforeUpdate(newList, oldMap);
    }
    public void beforeDelete(List<SObject> oldList) {
        if (isTriggerDisabled) return;
        this.doBeforeDelete(oldList);
    }
    public void afterInsert(List<SObject> newList) {
        if (isTriggerDisabled) return;
        this.doAfterInsert(newList);
    }
    public void afterUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        if (isTriggerDisabled) return;
        this.doAfterUpdate(newList, oldMap);
    }
    public void afterDelete(List<SObject> oldList) {
        if (isTriggerDisabled) return;
        this.doAfterDelete(oldList);
    }
    public void afterUndelete(List<SObject> newList) {
        if (isTriggerDisabled) return;
        this.doAfterUndelete(newList);
    }
    

    public virtual void doBeforeInsert(List<SObject> newList) {}
    public virtual void doBeforeUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {}
    public virtual void doBeforeDelete(List<SObject> oldList) {}
    public virtual void doAfterInsert(List<SObject> newList) {}
    public virtual void doAfterUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {}
    public virtual void doAfterDelete(List<SObject> oldList) {}
    public virtual void doAfterUndelete(List<SObject> newList) {}
}