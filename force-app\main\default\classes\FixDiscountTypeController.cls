public with sharing class FixDiscountTypeController {
    public FixDiscountTypeController() {

    }
    
   
    
   
    
    // 处理结果类
    private class ProcessResult {
        public List<QuoteLineItem> itemsToUpdate = new List<QuoteLineItem>();
        public List<QuoteLineItem> itemsToInsert = new List<QuoteLineItem>();
        public List<QuoteLineItem> itemsToDelete = new List<QuoteLineItem>();
    }
}