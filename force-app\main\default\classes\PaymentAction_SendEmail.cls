public with sharing class PaymentAction_SendEmail {
    
    @AuraEnabled(cacheable=true)
    public static string getAccountEmail(String recordId){
        String returnStr = '';
        try {
            BillPaymentAdvice__c payment = [SELECT Id,Account__c FROM BillPaymentAdvice__c WHERE Id =:recordId];
            List<Contact> paymentContact = [SELECT Id,AccountId,Contact_Type__c,Email FROM Contact WHERE Contact_Type__c ='账单' AND AccountId = :payment.Account__c LIMIT 1];
            if (!paymentContact.isEmpty()) {
                if (String.isBlank(paymentContact[0].Email)) {
                    returnStr = '请维护更新账单联系人email';
                }else{
                    returnStr = paymentContact[0].Email;
                }
                    
            }else{
                returnStr = '请添加账单联系人';
            }
            
        } catch (Exception e) {
                returnStr = '系统错误请联系管理员，错误信息：'+e.getMessage();
        }
        return returnStr;
    }

    @AuraEnabled(cacheable=true)
    public static string sendEmail(String recordId,String emailAddress){
        // 获取邮件模板
        EmailTemplate template = [SELECT Id FROM EmailTemplate 
                                WHERE DeveloperName = 'PaymentEmailToAccount' LIMIT 1];

        // 查询与记录关联的Content Document
        List<ContentDocumentLink> links = [
            SELECT ContentDocumentId 
            FROM ContentDocumentLink 
            WHERE LinkedEntityId = :recordId
            LIMIT 1
        ];
        if (links.isEmpty()) {
            return '请先打印付款通知书PDF预览后，再发送邮件给客户';
        }
        List<ContentVersion> cv = [SELECT VersionData, Title, FileExtension 
                        FROM ContentVersion 
                        WHERE ContentDocumentId = :links[0].ContentDocumentId 
                        AND IsLatest = true LIMIT 1];


        Messaging.EmailFileAttachment attachment = new Messaging.EmailFileAttachment();
        attachment.setFileName(cv[0].Title + '.' + cv[0].FileExtension);
        attachment.setBody(cv[0].VersionData);
        
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setTemplateId(template.Id);
        mail.setWhatId(recordId); // 关联到Account记录
        mail.setSaveAsActivity(true); // 保存为活动记录
        mail.setToAddresses(new String[] {emailAddress});
        mail.setSubject('付款通知书');
        mail.setFileAttachments(new Messaging.EmailFileAttachment[] {attachment});

        try{
            List<Messaging.SendEmailResult> results = Messaging.sendEmail(new Messaging.SingleEmailMessage[] {mail});
            // 检查每个发送结果
            for (Messaging.SendEmailResult result : results) {
                if (result.isSuccess()) {
                    System.debug('邮件发送成功');
                } else {
                    // 获取错误详情
                    List<Messaging.SendEmailError> errors = result.getErrors();
                    for (Messaging.SendEmailError error : errors) {
                        System.debug('发送失败: ' + error.getMessage());
                        System.debug('错误代码: ' + error.getStatusCode());
                        System.debug('错误字段: ' + error.getFields());
                    }
                }
            }
            return 'success';
        }catch(Exception e ){
            System.debug(e.getMessage());
            return e.getMessage();
        }

        
        
        
    }
        
}