<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>ProductGroup__c</field>
        <field>00NF300000Nl7tq</field>
        <field>phone__c</field>
        <field>CompanySize__c</field>
        <field>Contury__c</field>
        <field>City__c</field>
        <field>Customer_Invoice_Header__c</field>
        <field>X1__c</field>
        <field>TAX_Num1__c</field>
        <field>Tax_Categroy__c</field>
        <field>Street__c</field>
        <field>Registered_Country_Province__c</field>
        <field>Legal_Representative__c</field>
        <field>CurrencyIsoCode</field>
        <field>Customer_ICP_Number__c</field>
        <field>Customer_ICP_Filing_Number__c</field>
        <field>Risk_Attribute__c</field>
        <field>Customer_Importance_Level__c</field>
        <field>Customer_Mailing_Address__c</field>
        <field>Customer_Industry_Category__c</field>
        <field>Business_License_Number__c</field>
        <field>Customer_SimpleName__c</field>
        <field>Customer_Location_Region__c</field>
        <field>Industry__c</field>
        <field>Customer_Jurisdiction__c</field>
        <field>CustomerCode__c</field>
        <field>Postal_Code__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <label>商务经理</label>
        <name>ApprovalProcess</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Account_ProductManager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <label>产品经理</label>
        <name>ManagerApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>AccountEditAppliForm__c.CreatedBy</field>
                <operation>equals</operation>
                <value>Channel</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>ApproveRecord</ifCriteriaNotMet>
        <label>业务合作部</label>
        <name>BusinessCooApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>AreaManager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>区域经理</label>
        <name>AreaManagerApprovalProcess</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>AccountEditAppliForm__c.ApprovalStatus__c</field>
            <operation>equals</operation>
            <value>Draft,Rejected</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>ActionFieldUpdate1</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>ActionFieldUpdate2</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>ActionFieldUpdate</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>客户修改审批</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>0</processOrder>
    <recallActions>
        <action>
            <name>ActionFieldUpdate3</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
