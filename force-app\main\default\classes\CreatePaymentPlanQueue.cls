public class CreatePaymentPlanQueue implements Queueable {
    private Set<id> contractIds;
    public CreatePaymentPlanQueue(Set<id> contractids) {
        this.contractIds = contractids;
    }

    public void execute(QueueableContext context) {
        try {
            List<CollectionPlan__c> planList = new List<CollectionPlan__c>();
            map<id,List<Contract_Product__c>> contractProductMap = new Map<id,List<Contract_Product__c>>();
            map<id,List<Installments_Item__c>> contractInstallmentMap = new Map<id,List<Installments_Item__c>>();
            for(contract con:[Select id,account_Period__c,Number_of_Installments__c,Total_Installment_Amount__c,FirstPayment__c,DepositAmount__c,Service_Start__c,Service_End__c,Expense_Settlement_Method__c,Payment_Cycle__c,(Select id,Product__c,ValidFrom__c,ValidTo__c from Contract_Products__r),(Select id,InstallNo__c,Amount__c,StartDate__c,EndDate__c from Installments_Items__r) FROM contract where id IN :contractIds]){
                CollectionPlan__c collectionPlan = new CollectionPlan__c();
                collectionPlan.Service_StartDate__c = con.Service_Start__c;
                collectionPlan.Service_EndDate__c = con.Service_End__c;
                collectionPlan.Expense_Settlement_Method__c = con.Expense_Settlement_Method__c;
                collectionPlan.PaymentCycle__c = con.Payment_Cycle__c;
                collectionPlan.Contract__c = con.id;
                collectionPlan.Externalid__c = con.id;
                collectionPlan.Number_of_Installments__c = con.Number_of_Installments__c;
                collectionPlan.Total_Installment_Amount__c = con.Total_Installment_Amount__c;
                collectionPlan.Deposit_Tax__c = con.DepositAmount__c;
                collectionPlan.AdvancePayment__c = con.FirstPayment__c;
                collectionPlan.account_Period__c = con.account_Period__c;
                planList.add(collectionPlan);
                contractProductMap.put(con.id,con.Contract_Products__r);
                contractInstallmentMap.put(con.id,con.Installments_Items__r);
            }
            if(planList.size() > 0) upsert planList Externalid__c;
            List<CollectionPlanLine__c> planLineList = new List<CollectionPlanLine__c>();
            for(CollectionPlan__c cp:planList){
                list<CollectionPlanLine__c> returnList = getCollectionPlanDetail(cp,contractInstallmentMap);
                planLineList.addAll(returnList);
            }
            if(planLineList.size() > 0) upsert planLineList ExternalId__c;
            List<CollectionPlanLineProduct__c> planLineProductList = new List<CollectionPlanLineProduct__c>();
            for(CollectionPlanLine__c cpl:planLineList){
                if(cpl.isSpecial__c || cpl.isInstallment__c)continue;
                List<CollectionPlanLineProduct__c> returnProdList = getCollectionPlanLineProduct(cpl,contractProductMap);
                planLineProductList.addAll(returnProdList);
            }
            if(planLineProductList.size() > 0) upsert planLineProductList ExternalId__c;

            Interface_Log__c queueLog = new Interface_Log__c();
            queueLog.InterfaceName__c = 'CreatePaymentPlanQueue';
            queueLog.Status__c = 'Success';
            queueLog.InterfaceType__c = 'queue';
            queueLog.RecordIDs__c = String.join(contractIds, ';');
            queueLog.CallTime__c = System.now();
            insert queueLog;

            //同步SAP
            system.enqueueJob(new SyncContractToSapQueue(contractIds));

            

        } catch (Exception e) {
            Interface_Log__c queueLog = new Interface_Log__c();
            queueLog.InterfaceName__c = 'CreatePaymentPlanQueue';
            queueLog.Status__c = 'Failed';
            queueLog.InterfaceType__c = 'queue';
            queueLog.RecordIDs__c = String.join(contractIds, ';');
            queueLog.CallTime__c = System.now();
            queueLog.ErrorMessage__c = e.getMessage();
            
            insert queueLog;
        }
        
    }

    public list<CollectionPlanLine__c> getCollectionPlanDetail(CollectionPlan__c plan,map<id,List<Installments_Item__c>> installmentMap){
        list<CollectionPlanLine__c> result = new list<CollectionPlanLine__c>();
        String periodType = plan.PaymentCycle__c;
        String expenseType = plan.Expense_Settlement_Method__c;
        Date endDate = plan.Service_EndDate__c;
        Date startDate = plan.Service_StartDate__c;
        Date currentStart = startDate;
        Date todayD = system.today();
        Integer i = 0;
        // 文本格式如“30天”，去掉“天”后转换成整数
        Integer accDays = 0;
        if(plan.account_Period__c != null){
            String accStr = plan.account_Period__c.replace('天', '').trim();
            accDays = Integer.valueOf(accStr);
        }
        if (startDate == null || endDate == null || startDate > endDate) {
            throw new IllegalArgumentException('日期参数不合法');
        }
        //分期
        if(expenseType == 'Installment'){
            //分期的逻辑
            if(installmentMap.containsKey(plan.Contract__c) && installmentMap.get(plan.Contract__c) != null ){
                for(Installments_Item__c ii:installmentMap.get(plan.Contract__c)){
                    CollectionPlanLine__c cpl = new CollectionPlanLine__c();
                    cpl.Period_StartDate__c = ii.StartDate__c ;
                    cpl.Period_EndDate__c = ii.EndDate__c;
                    cpl.PaymentDate__c = cpl.Period_EndDate__c.addDays(accDays);
                    if(cpl.Period_EndDate__c <= todayD )cpl.isPreActived__c = true;
                    cpl.CollectionPlan__c = plan.id;
                    cpl.ContractId__c = plan.Contract__c;
                    cpl.Payment_Notice_Amt__c = ii.Amount__c;
                    cpl.ExternalId__c = plan.id + '_' + String.valueof(cpl.Period_EndDate__c.year()) + '_' + String.valueof(cpl.Period_EndDate__c.Month()) + String.valueof(cpl.Period_EndDate__c.Day());
                    cpl.Description__c = '第 ' + ii.InstallNo__c +  ' 期收款计划明细';
                    cpl.PlanNo__c = ii.InstallNo__c;
                    cpl.isInstallment__c = true;
                    result.add(cpl);
                }
            } 
        }else{
            while (currentStart <= endDate) {
                Date currentEnd;
                i++;
                if (periodType == 'byMonth') {
                    currentEnd = Date.newInstance(currentStart.year(), currentStart.month(), 1)
                        .addMonths(1).addDays(-1);
                } else if (periodType == 'byQuarter') {
                    Integer monthIndex = currentStart.month() - 1;
                    Integer mod3 = monthIndex - (Math.floor(monthIndex / 3) * 3).intValue();
                    Integer addMonths = 3 - mod3;
                    currentEnd = Date.newInstance(currentStart.year(), currentStart.month(), 1)
                        .addMonths(addMonths).addDays(-1);
                } else if (periodType == 'byHalfYear') {
                    Integer monthIndex = currentStart.month() - 1;
                    Integer mod6 = monthIndex - (Math.floor(monthIndex / 6) * 6).intValue();
                    Integer addMonths = 6 - mod6;
                    currentEnd = Date.newInstance(currentStart.year(), currentStart.month(), 1)
                        .addMonths(addMonths).addDays(-1);
                } else if (periodType == 'byYear') {
                    currentEnd = Date.newInstance(currentStart.year() + 1, 1, 1).addDays(-1);
                } else {
                    throw new IllegalArgumentException('周期参数无效：'+periodType);
                }
                // 不能超过总的endDate
                if (currentEnd > endDate) {
                    currentEnd = endDate;
                }

                CollectionPlanLine__c cpl = new CollectionPlanLine__c();
                cpl.Period_StartDate__c = currentStart;
                cpl.Period_EndDate__c = currentEnd;
                cpl.PaymentDate__c = cpl.Period_EndDate__c.addDays(accDays);
                if(cpl.Period_EndDate__c <= todayD )cpl.isPreActived__c = true;
                cpl.CollectionPlan__c = plan.id;
                cpl.ContractId__c = plan.Contract__c;
                cpl.ExternalId__c = plan.id + '_' + String.valueof(currentEnd.year()) + '_' + String.valueof(currentEnd.Month());
                cpl.Description__c = '第 ' + i +  ' 期收款计划明细';
                cpl.PlanNo__c = i;
                result.add(cpl);

                currentStart = currentEnd.addDays(1);
            }

            if(expenseType == 'AdvancePay' && plan.AdvancePayment__c != null && plan.AdvancePayment__c != 0){
                CollectionPlanLine__c cplA = new CollectionPlanLine__c();
                cplA.Period_StartDate__c = system.TODAY();
                cplA.Period_EndDate__c = system.TODAY();
                cplA.Payment_Notice_Amt__c = plan.AdvancePayment__c;
                cplA.CollectionPlan__c = plan.id;
                cplA.ContractId__c = plan.Contract__c;
                cplA.ExternalId__c = plan.id + '_' + String.valueof(system.TODAY().year()) + '_' + String.valueof(system.TODAY().Month()) + '_AdvancePay';
                cplA.Description__c = '收款计划明细-预付款';
                cplA.isSpecial__c = true;
                cplA.PlanNo__c = 0;
                result.add(cplA);
            }else if(expenseType == 'Deposit​​AndPostPay' && plan.Deposit_Tax__c != null && plan.Deposit_Tax__c != 0 ){
                CollectionPlanLine__c cplD = new CollectionPlanLine__c();
                cplD.Period_StartDate__c = system.TODAY();
                cplD.Period_EndDate__c = system.TODAY();
                cplD.Payment_Notice_Amt__c = plan.Deposit_Tax__c;
                cplD.CollectionPlan__c = plan.id;
                cplD.ContractId__c = plan.Contract__c;
                cplD.ExternalId__c = plan.id + '_' + String.valueof(system.TODAY().year()) + '_' + String.valueof(system.TODAY().Month()) + '_AdvancePay';
                cplD.Description__c = '收款计划明细-押金';
                cplD.isSpecial__c = true;
                cplD.PlanNo__c = 0;
                result.add(cplD);
            }
        }
        return result;
    }

    public List<CollectionPlanLineProduct__c> getCollectionPlanLineProduct(CollectionPlanLine__c planLine,map<id,List<Contract_Product__c>> contractProductMap){

        List<CollectionPlanLineProduct__c> result = new List<CollectionPlanLineProduct__c>();
        String contractId = planLine.ContractId__c;
        if(contractId != null && contractProductMap.containsKey(contractId) ){
            List<Contract_Product__c> contractProductList = contractProductMap.get(contractId);
            for(Contract_Product__c cp:contractProductList){
                Date startDate = cp.ValidFrom__c;
                Date endDate = cp.ValidTo__c;
                if (startDate == null || endDate == null || startDate > endDate || planLine.Period_StartDate__c == null || planLine.Period_EndDate__c == null) {
                    throw new IllegalArgumentException('日期参数不合法');
                }
                CollectionPlanLineProduct__c lineProduct = new CollectionPlanLineProduct__c();
                lineProduct.CollectionPlanLine__c = planLine.Id;
                lineProduct.Product__c = cp.Product__c;
                lineProduct.ContractProduct__c = cp.id;
                lineProduct.ExternalId__c = String.valueOf(planLine.Id) + String.valueOf(cp.Product__c);
                if(startDate >= planLine.Period_StartDate__c && startDate <= planLine.Period_EndDate__c && endDate >= planLine.Period_StartDate__c && endDate <= planLine.Period_EndDate__c){
                    // ┌─────planLine区间A─────────┐
                    //      ┌─product区间B────┐
                    lineProduct.StartDate__c = startDate;
                    lineProduct.EndDate__c = endDate;
                    result.add(lineProduct);
                }else if( endDate >= planLine.Period_StartDate__c && endDate <= planLine.Period_EndDate__c && startDate < planLine.Period_StartDate__c  ){
                    //     ┌─────planLine区间A──────┐
                    //┌──────────product区间B────┐
                    lineProduct.StartDate__c = planLine.Period_StartDate__c;
                    lineProduct.EndDate__c = endDate;
                    result.add(lineProduct);
                }else if(startDate >= planLine.Period_StartDate__c && startDate <= planLine.Period_EndDate__c &&  endDate > planLine.Period_EndDate__c ){
                    // ┌─────planLine区间A──────┐
                    //      ┌─────product区间B───────┐
                    lineProduct.StartDate__c = startDate;
                    lineProduct.EndDate__c = planLine.Period_EndDate__c;
                    result.add(lineProduct);
                }else if(endDate >  planLine.Period_EndDate__c && startDate < planLine.Period_StartDate__c){
                    //      ┌──planLine区间A───┐
                    // ┌───────product区间B─────────┐
                    lineProduct.StartDate__c = planLine.Period_StartDate__c;
                    lineProduct.EndDate__c = planLine.Period_EndDate__c;
                    result.add(lineProduct);
                }else if(startDate > planLine.Period_EndDate__c || endDate < planLine.Period_StartDate__c){
                    // ┌──planLine区间A───┐
                    //                       ┌────product区间B────┐
                }
            }
        }

        return result;
    }
}