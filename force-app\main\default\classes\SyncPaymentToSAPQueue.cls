public without sharing class SyncPaymentToSAPQueue implements Queueable {
    Set<Id> paymentIds ;
    public SyncPaymentToSAPQueue(Set<Id> paymentIds) {
        this.paymentIds = paymentIds;
    }

    public void execute(QueueableContext context) {
        if(paymentIds == null || paymentIds.isEmpty() || paymentIds.size()>50){ 
            return;
        }
        for(Id paymentId : paymentIds){
            Interface_CRMSyncPaymentToSAP syncRequest = new Interface_CRMSyncPaymentToSAP(paymentId);
        }
    }
}