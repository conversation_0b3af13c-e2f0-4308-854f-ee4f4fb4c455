/**
 * Interface_ProductPriceObject
 * 描述：用于表示从SAP系统接收的产品牌价数据结构
 */
public class Interface_ProductPriceObject {
    public String UUID { get; set; }  // 接口唯一标识符
    public String ZNUMB { get; set; } // 接口编号
    public String SYSID { get; set; } // 请求系统
    
    // 牌价项目列表
    public List<PriceItem> TLIST;
    

    //牌价
    public class PriceItem {
        
        public String KNUMH { get; set; }  //条件记录编号
        public String VKORG { get; set; } // 销售组织
        public String VTWEG { get; set; } // 分销渠道
        public String ZAREA { get; set; } // 产品区域
        public String MATNR { get; set; } // 物料编码
        public String KBETR { get; set; } // 金额
        public String KONWA { get; set; } // 币种
        public String KPEIN { get; set; } // 价格单位
        public String KMEIN { get; set; } // 计量单位
        public String DATAB { get; set; } // 有效起始日期(YYYYMMDD)
        public String DATBI { get; set; } // 有效结束日期(YYYYMMDD)
        public List<PriceGrade> ITEM { get; set; } // 价格层级列表
    }

    //定价等级
    public class PriceGrade {
        
        public String KLFN1 { get; set; } // 等级编码
        public String KSTBM { get; set; } // 等级数量
        public String KBETR { get; set; } // 区间价格
    } 
    

    //格式化日期，输入20250712，输出日期格式
    public static Date parseSAPDate(String sapDate) {
        if (sapDate == null || sapDate == '99991231') {
            return null;
        }
        Integer year = Integer.valueOf(sapDate.substring(0, 4));
        Integer month = Integer.valueOf(sapDate.substring(4, 6));
        Integer day = Integer.valueOf(sapDate.substring(6, 8));
        return Date.newInstance(year, month, day);
    }
}