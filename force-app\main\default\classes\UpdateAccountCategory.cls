public with sharing class UpdateAccountCategory {
    public static void doUpdateAccountCategory(List<Account> listAccount){
        //获取创建Account的用户信息
        Set<Id> createUserIdSet = new Set<Id>();
        for (Account acc : listAccount) {
            if (String.isEmpty(acc.Customer_Category__c)) {
                System.debug(acc.OwnerId);
                createUserIdSet.add(acc.OwnerId);
            }
        }
        Map<Id,User> users = new Map<Id, User>([SELECT Id,IsChannelUser__c FROM User WHERE Id IN :createUserIdSet]);
        for (Account acc : listAccount) {
            if (String.isEmpty(acc.Customer_Category__c)&&users.get(acc.OwnerId).IsChannelUser__c.equals(true) ){
                acc.Customer_Category__c = '渠道客户';
            }
        }
    }
}